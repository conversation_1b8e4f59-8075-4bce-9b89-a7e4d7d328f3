#ifndef CARDTEMPLATE_H
#define CARDTEMPLATE_H

#include <QDateTime>
#include <QFrame>
#include <QLabel>
#include <QMouseEvent>
#include <QShowEvent>

#include "DeviceManager.h"
namespace Ui {
class CardTemplate;
}

class CardTemplate : public QFrame
{
    Q_OBJECT

public:
    explicit CardTemplate(QWidget* parent = nullptr);
    ~CardTemplate();

    // 设置卡片数据
    void setData(const QString& title, const QString& unit);
    void setValue(const float& value, const QString& status, const QString& faultCode);

signals:
    void clicked();

private slots:
    void onCommStatusChanged(const bool& success);
    void onDeviceDataChanged(const DeviceData& data);

protected:
    void mousePressEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void enterEvent(QEvent* event) override;
    void leaveEvent(QEvent* event) override;

private:
    void changeEvent(QEvent* event) override;
    void showEvent(QShowEvent* event) override;
    void hideEvent(QHideEvent* event) override;
    Ui::CardTemplate* ui;
    QString m_deviceId;
    bool m_pressed = false;
};

#endif  // CARDTEMPLATE_H