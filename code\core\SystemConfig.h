#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#include <QDateTime>
#include <QObject>
#include <QString>

// 系统配置结构体
struct SystemConfig {
    int scheduled = 0;                  // 定时测量按位存储(0-23点)
    int zeroCheckIntervalDays = 0;      // 零点核查间隔(天)
    int zeroCheckTime = 0;              // 零点核查时间
    int stdCheckIntervalDays = 0;       // 标液核查间隔(天)
    int stdCheckTime = 0;               // 标液核查时间
    int calibIntervalDays = 0;          // 校正标定间隔(天)
    int calibTime = 0;                  // 校正标定时间
    int spikeRecoveryIntervalDays = 0;  // 加标回收间隔(天)
    int spikeRecoveryTime = 0;          // 加标回收时间
    int cleanIntervalDays = 0;          // 仪表清洗间隔(天)
    int cleanTime = 0;                  // 仪表清洗时间
};

/**
 * @brief 系统配置管理类
 * 负责配置的加载、保存、验证等功能
 */
class SystemConfigManager : public QObject
{
    Q_OBJECT

public:
    static SystemConfigManager* instance();

    // 配置操作
    SystemConfig getConfig() const { return m_config; }
    bool setConfig(const SystemConfig& config);

    // 配置验证
    bool validateConfig(const SystemConfig& config, QString* errorMessage = nullptr);
    bool checkTimeConflict(const SystemConfig& config);

    // 调度相关查询
    bool isScheduled(int hour) const;

signals:
    void configChanged(const SystemConfig& config);

private:
    explicit SystemConfigManager(QObject* parent = nullptr);
    bool loadConfig();
    bool saveConfig();
    SystemConfig m_config;
};

#endif  // SYSTEM_CONFIG_H