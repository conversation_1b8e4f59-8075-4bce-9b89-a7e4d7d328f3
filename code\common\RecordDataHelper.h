#ifndef RECORDDATAHELPER_H
#define RECORDDATAHELPER_H

#include <QDate>
#include <QDebug>
#include <QStringList>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QVariantMap>
#include <QVector>

// 记录类型枚举
enum class RecordType {
    DeviceData = 1,  // 设备数据记录
    UploadData = 2,  // 上传记录
    ErrorData = 3,   // 错误记录
};

class RecordDataHelper
{
public:
    // 核心功能方法
    static void setupTableHeaders(QTableWidget *tableWidget, RecordType recordType);
    static void loadPageData(QTableWidget *tableWidget, RecordType recordType, int currentPage,
                             int pageSize, const QDate &queryDate);
    static int getTotalRecords(RecordType recordType, const QDate &queryDate);

private:
    // 设备数据相关方法
    static void setupDeviceDataHeaders(QTableWidget *tableWidget);
    static void loadDeviceData(QTableWidget *tableWidget, int currentPage, int pageSize,
                               const QDate &queryDate);

    // 上传记录相关方法
    static void setupUploadRecordHeaders(QTableWidget *tableWidget);
    static void loadUploadRecordData(QTableWidget *tableWidget, int currentPage, int pageSize,
                                     const QDate &queryDate);

    // 错误记录相关方法
    static void setupErrorLogHeaders(QTableWidget *tableWidget);
    static void loadErrorLogData(QTableWidget *tableWidget, int currentPage, int pageSize,
                                 const QDate &queryDate);
    // 禁止实例化
    RecordDataHelper() = delete;
    ~RecordDataHelper() = delete;
    RecordDataHelper(const RecordDataHelper &) = delete;
    RecordDataHelper &operator=(const RecordDataHelper &) = delete;
};

#endif  // RECORDDATAHELPER_H