#ifndef NOTIFICATION_MANAGER_H
#define NOTIFICATION_MANAGER_H

#include <QFrame>
#include <QGraphicsDropShadowEffect>
#include <QHBoxLayout>
#include <QLabel>
#include <QObject>
#include <QPropertyAnimation>
#include <QPushButton>
#include <QQueue>
#include <QString>
#include <QTimer>
#include <QVBoxLayout>
#include <QWidget>

// 通知类型枚举
enum class NotificationType { Info, Warning, Error, Success };

// 通知数据结构
struct NotificationData {
    QString title;
    QString message;
    NotificationType type;
    int duration;  // 持续时间（毫秒），0表示手动关闭

    NotificationData(const QString& t = "", const QString& m = "",
                     NotificationType ty = NotificationType::Info, int d = 3000)
        : title(t), message(m), type(ty), duration(d)
    {
    }
};

// 单个通知弹窗组件
class NotificationWidget : public QFrame
{
    Q_OBJECT

public:
    explicit NotificationWidget(const NotificationData& data, QWidget* parent = nullptr);
    ~NotificationWidget();

    void showNotification();
    void hideNotification();

signals:
    void notificationClosed();

private slots:
    void onCloseButtonClicked();
    void onAutoHideTimeout();

private:
    void setupUI();
    void applyStyle();
    void setupAnimations();
    QString getIconText(NotificationType type);
    QString getTypeColors(NotificationType type, QString& borderColor, QString& backgroundColor,
                          QString& iconColor);

    NotificationData m_data;
    QLabel* m_iconLabel;
    QLabel* m_titleLabel;
    QLabel* m_messageLabel;
    QPushButton* m_closeButton;
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_headerLayout;
    QHBoxLayout* m_contentLayout;
    QFrame* m_progressBar;

    QPropertyAnimation* m_positionAnimation;
    QPropertyAnimation* m_progressAnimation;
    QTimer* m_autoHideTimer;
};

// 通知管理器 - 单例模式
class NotificationManager : public QObject
{
    Q_OBJECT

public:
    static NotificationManager* instance();

    // 添加通知到队列
    void showNotification(const QString& title, const QString& message,
                          NotificationType type = NotificationType::Info, int duration = 3000);

    void showInfo(const QString& message, int duration = 3000);
    void showWarning(const QString& message, int duration = 4000);
    void showError(const QString& message, int duration = 5000);
    void showSuccess(const QString& message, int duration = 3000);

    // 设置通知显示的父窗口
    void setParentWidget(QWidget* parent);

    // 清空所有通知
    void clearAll();

private slots:
    void onNotificationClosed();
    void processNotificationQueue();

private:
    explicit NotificationManager(QObject* parent = nullptr);
    ~NotificationManager();

    void showNextNotification();
    void repositionNotifications();

    static NotificationManager* m_instance;

    QWidget* m_parentWidget;
    QQueue<NotificationData> m_notificationQueue;
    QList<NotificationWidget*> m_activeNotifications;

    const int MAX_NOTIFICATIONS = 5;      // 最大同时显示的通知数量
    const int NOTIFICATION_SPACING = 12;  // 通知之间的间距
    const int MARGIN_RIGHT = 20;          // 右边距
    const int MARGIN_TOP = 20;            // 上边距
};

#endif  // NOTIFICATION_MANAGER_H