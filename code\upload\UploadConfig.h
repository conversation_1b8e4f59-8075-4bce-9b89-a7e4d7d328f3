#ifndef UPLOADCONFIG_H
#define UPLOADCONFIG_H
#include <QMap>
#include <QObject>
namespace Upload {

struct Config {
    QString IP;
    quint16 port;
    QString MN;
    bool enabled;
};

class Configurer : public QObject
{
    Q_OBJECT
public:
    static Configurer *instance();
    QMap<QString, Config> getConfig() const { return m_configMap; }
    bool setConfig(const QMap<QString, Config> &configMap);

private:
    Configurer();
    ~Configurer();
    Configurer(const Configurer &other) = delete;
    Configurer &operator=(const Configurer &other) = delete;
    bool loadConfig();
    bool saveConfig();
    void defaultConfig();
    QMap<QString, Config> m_configMap;
};
}  // namespace Upload

// 声明Config为Qt元类型，支持在信号槽中传递
Q_DECLARE_METATYPE(Upload::Config)
#endif  // UPLOADCONFIG_H
