# Security Policy

## Reporting a Vulnerability

We value the security of our users and appreciate your efforts to responsibly disclose vulnerabilities. If you have
identified a security vulnerability in this repository, please use the GitHub Security Advisory
["Report a Vulnerability"](https://github.com/nlohmann/json/security/advisories/new) tab.

Until it is published, this draft security advisory will only be visible to the maintainers of this project. Other
users and teams may be added once the advisory is created.

We will send a response indicating the next steps in handling your report. After the initial reply to your report, we
will keep you informed of the progress towards a fix and full announcement and may ask for additional information or
guidance.

For vulnerabilities in third-party dependencies or modules, please report them directly to the respective maintainers.

## Additional Resources

- Explore security-related topics and contribute to tools and projects through
  [GitHub Security Lab](https://securitylab.github.com/).
- Learn more about responsible disclosure and reporting vulnerabilities in GitHub at
  [About coordinated disclosure of security vulnerabilities](https://docs.github.com/en/code-security/repository-security-advisories/about-coordinated-disclosure-of-security-vulnerabilities).

We sincerely thank you for contributing to the security and integrity of this project!
