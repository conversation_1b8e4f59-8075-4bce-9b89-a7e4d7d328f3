#ifndef DEVICEHELPER_H
#define DEVICEHELPER_H

#include <QDateTime>
#include <QObject>
#include <QTimer>

#include "IDevice.h"
#include "ModbusManager.h"
struct DeviceConfig {
    QString name;                 // 设备名称
    float analyze_upperLimit;     // 报警上限浓度
    float analyze_current_range;  // 当前量程
    float inspect_std_conc;       // 标液核查浓度
    float cali_std1_conc;         // 标一浓度
    float cali_std2_conc;         // 标二浓度
};

class DeviceHelper : public IDevice
{
public:
    DeviceHelper(QString deviceName, DeviceConfig deviceConfig, QObject* parent = nullptr);
    ~DeviceHelper();
    // Modbus操作方法
    void setDeviceTime(const QDateTime& datetime) override;
    void control(quint16 optCode) override;
    void control(quint16 addr, quint16 data) override;
signals:

private slots:
    void onGetDeviceData() override;

private:
    DeviceConfig m_deviceConfig;
};

#endif  // !DEVICE_MANAGER_H
