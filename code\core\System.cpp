#include "System.h"

#include <QDebug>

System* System::instance()
{
    static System instance;
    return &instance;
}

System::System(QObject* parent) : QObject(parent)
{
    // 初始化子系统
    m_configManager = SystemConfigManager::instance();
    m_scheduler = new SystemScheduler(this);

    // 建立连接
    initConnections();
}

System::~System() {}

void System::initConnections()
{
    // 转发配置管理器的信号
    connect(m_configManager, &SystemConfigManager::configChanged, this, &System::configChanged);

    // 转发调度器的信号
    connect(m_scheduler, &SystemScheduler::infoChanged, this, &System::infoChanged);
    connect(m_scheduler, &SystemScheduler::isRunningChanged, this, &System::isRunningChanged);
    connect(m_scheduler, &SystemScheduler::currentTaskChanged, this, &System::currentTaskChanged);
    connect(m_scheduler, &SystemScheduler::taskFailed, this, &System::taskFailed);

    // 监听配置变化，通知调度器更新
    connect(m_configManager, &SystemConfigManager::configChanged, m_scheduler,
            &SystemScheduler::updateNextTimes);
}

void System::start()
{
    qDebug() << "System: 启动系统";
    m_scheduler->start();
}

void System::stop()
{
    qDebug() << "System: 停止系统";
    m_scheduler->stop();
}

bool System::isRunning() const { return m_scheduler->isRunning(); }

SystemConfig System::getConfig() const { return m_configManager->getConfig(); }

bool System::setConfig(const SystemConfig& config) { return m_configManager->setConfig(config); }

bool System::checkConfigTimeConflict(const SystemConfig& config)
{
    return m_configManager->checkTimeConflict(config);
}

bool System::isScheduled(int hour) const { return m_configManager->isScheduled(hour); }

SystemInfo System::getInfo() const { return m_scheduler->getInfo(); }

QString System::getTaskName(qint16 operateCode) const
{
    return m_scheduler->getTaskName(operateCode);
}

void System::execTask(qint16 operateCode) { m_scheduler->executeTask(operateCode); }