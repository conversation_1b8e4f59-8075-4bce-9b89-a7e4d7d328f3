QWidget{ /* 全局 */
    background-color: #34495e;
    color: #b1cdd4;
    border-color: #6c7274;
	font-family: "Microsoft YaHei";
}
QWidget#widget_status{ /* 状态栏 */
    border-top-width: 1px;
    border-top-style: solid;
}
QWidget#widget_title{ /* 标题栏 */
    border-bottom-width: 1px;
    border-bottom-style: solid;
}
QStackedWidget#stackedWidget{ /* 主窗口 */
    font-size: 14px;
}

QWidget#widget_tools{ /*菜单栏*/
    border-right-width: 1px;
    border-right-style: solid;
}
QWidget#widget_tools QToolButton,QWidget#widget_tools QPushButton {
    background-color: transparent;
    color: #b1cdd4;
    border: none;
    padding-top: 10px;
    padding-bottom: 10px;
    min-width: 70px;
    font-family: "Microsoft YaHei";
    font-size: 12px;
    font-weight: bold
}
QWidget#widget_tools QToolButton:checked {
    background-color: #30618a;
    border-left: 3px solid #538fc0;  /* 可以添加一个左边框来表示选中状态 */
    font-size: 14px;
}
QWidget#widget_tools QToolButton:hover {
    qproperty-iconSize: 45px 45px;
    background-color: rgba(33, 150, 243, 0.1);
    /* color: gray; */
    font-weight: bold;
}
QWidget#widget_tools QToolButton:pressed {
    background-color: rgba(33, 150, 243, 0.2);  /* 点击时加深背景色 */
    padding-left: 7px;  /* 轻微的按压效果 */
    padding-top: 7px;
}
/* "启动"按钮 */
QPushButton#startButton {
    background-color: #48c9b0; 
    border-color: #48c9b0;
    color: white;
    border-radius: 5px; 
    font-weight: bold;
}
QPushButton#startButton:hover {
    background-color: #45b39d;
    border-color: #45b39d;
}
QPushButton#startButton:pressed {
    background-color: #45b39d;
    border-color: #45b39d;
    padding-left: 3px;  /* 轻微的按压效果 */
    padding-top: 3px;
}
QPushButton#startButton:disabled {
    background-color: #b0bec5; 
    border-color: #b0bec5;
    color: #586b75; 
}
/* "停止"按钮 */
QPushButton#stopButton {
    background-color: #ff6d40; 
    border-color: #ff6d40;
    color: white;
    border-radius: 5px; 
    font-weight: bold;
}
QPushButton#stopButton:hover {
    background-color: #FF5722; 
    border-color: #FF5722;
}
QPushButton#stopButton:pressed {
    background-color: #c74017;
    border-color: #c74017;
    padding-left: 3px; 
    padding-top: 3px;
} 
QLabel {
    background-color :transparent;
    color: #b1cdd4;
    /* min-height: 25px; */
    font-size: 14px;
}
QComboBox {
    background-color: transparent;
    color: #b1cdd4; 
    border: 1px solid #6c7274;
    border-radius: 3px;
    font-size: 14px;
    min-height: 25px;
}

QComboBox QAbstractItemView {
    background-color: #2c3e50; /* 下拉列表的深色背景 */
    color: #b1cdd4; /* 列表项文字颜色 */
    border: 1px solid #6c7274; /* 列表边框 */
    selection-background-color: #48c9b0; /* 选中项背景色 (使用启动按钮的颜色) */
    selection-color: white; /* 选中项文字颜色 */
    outline: 0px; /* 移除焦点时的虚线框 */
}

QComboBox QAbstractItemView::item {
    min-height: 30px; /* 列表项最小高度 */
    padding: 10px 18px; /* 列表项内边距 */
    border-radius: 2px; /* 轻微圆角 */
    margin-bottom: 14px; 
}

QComboBox QAbstractItemView::item:hover {
    background-color: #34495e; /* 悬停时背景色 (与主窗口背景类似) */
    color: white;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #48c9b0; /* 确保与 selection-background-color 一致 */
    color: white; /* 确保与 selection-color 一致 */
}
/* 通用按钮样式 */
QPushButton {
    background-color: #3d84a8; /* Indigo 300 - 柔和靛蓝色 */
    border: 1px solid #3d84a8;
    color: #b1cdd4;             /* 白色文字 */
    min-width: 80px;
    min-height: 30px;
    border-radius: 5px;
    font-weight: bold;
}
QPushButton:hover {
    /* background-color: #5d84a8;  */
    font-size: 14px;
}
QPushButton:pressed {
    background-color: #2d84a8; /* Indigo 500 - 按下时略深 */
    padding-left: 2px;
    padding-top: 2px;
}
QPushButton:disabled {
    background-color: #37474F; /* Blue Grey 800 - 禁用时更暗 */
    border-color: #37474F;    /* 与背景色一致 */
    color: #78909C;           /* Blue Grey 400 - 禁用时文字颜色 */
}

QTextEdit {
    background-color: transparent;
    border-radius: 4px;
    padding: 5px;
    min-height: 30px;
    font-size: 12px;
    font-weight: bold;
}
QLineEdit {
    background-color: transparent;
    color: #b1cdd4;
    border: 1px solid #6c7274;
    border-radius: 3px;
    padding: 0px;
    font-size: 14px;
}
QCheckBox::indicator {
	margin-top: 2px;
	margin-left: 25px;
}
QSpinBox, QDoubleSpinBox {
    background-color: transparent;
    color: #b1cdd4;
    border: 1px solid #6c7274;
    border-radius: 3px;
    font-size: 14px;
    /* min-height: 25px; */
}
QSpinBox::up-button, QDoubleSpinBox::up-button {
    width: 0px;
}
QSpinBox::down-button, QDoubleSpinBox::down-button {
    width: 0px;
}
QDateEdit{
    background-color: transparent;
    color: #b1cdd4;
    border-bottom: 1px solid #6c7274;
    border-radius: 0px;
    font-size: 13px;
    font-weight: bold;
}
QDateEdit::up-button, QDateEdit::down-button {
    width: 0px;
}
QGroupBox {
    border: 1px solid #6c7274;
    border-radius: 5px;
    margin-top: 9px;  /* 为标题留出空间 */
	font-size: 14px;
	font-weight: bold
}

QGroupBox::title {
    subcontrol-origin: margin;  /* 标题位置相对于margin */
    subcontrol-position: top left; /* 标题位置在左上角 */
    padding: 0 3px;  /* 标题文字的左右内边距 */
    left: 10px;      /* 标题距离左边的距离 */
	color: #8390a7;
    font-size: 12px;
}
/* 表格 */
QTableWidget {
    border: none;
    background-color: #34495E;
    color: #D5D8DC;
    gridline-color: transparent; 
    alternate-background-color: #2C3E50; /* 偶数行背景色*/
    outline: none; /*去除表格本身的焦点虚线框 */
}
/* 表头样式 */
QHeaderView::section {
    background-color: #2C3E50;
    color: #ECF0F1;
    border: none;
    min-height: 30px;
    font-weight: bold;
    font-size: 14px;
}
QTableWidget::item {
    padding: 5px;
    border: none;
}
QTableWidget::item:selected {
    background-color: #3f72af; 
    color: #FFFFFF;
    border: none;
    font-weight: bold; 
    font-size: 12px; 
}
QMessageBox {
    background-color: #34495e;
    color: #b1cdd4;
}
QMessageBox QLabel {
    color: #b1cdd4;
}

QFrame#CardTemplate {
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #364f6b, stop: 1 #112d4e);
    border: 1px solid #4A5B6F;
    border-radius: 8px;
}

QFrame#CardTemplate[hovered="true"] {
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #3d5a78, stop: 1 #1a3555);
}

QFrame#CardTemplate[pressed="true"] {
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #2d4a60, stop: 1 #0d1f3a);
    border: 2px solid #6E7F93;
}
QFrame#CardTemplate:disabled {
    background-color: #394658;
    border: 1px solid #4A5B6F; 
}
QFrame#CardTemplate QLabel#titleLabel {
    background-color: transparent;
    font-size: 18px;
    font-weight: bold;
    color: #b1cdd4;
    padding: 10px;
}

QFrame#CardTemplate QLabel#valueLabel {
    background-color: transparent;
    font-size: 38px;
    font-weight: bold;
    min-height: 40px;
    color: #4FC3F7;
    padding: 1px;
}
QFrame#CardTemplate QLabel#valueLabel:disabled {
    color: #707580;
}
QFrame#CardTemplate QLabel#unitLabel {
    background-color: transparent;
    font-size: 16px;
    color: #A0B4C8;
    padding: 1px;
}

QProgressBar {
    border: 1px solid #6c7274; /* 与其他输入框和容器边框颜色一致 */
    border-radius: 5px;        /* 轻微圆角 */
    background-color: #2c3e50; /* 深色背景，比主背景略亮或不同，以区分 */
    text-align: center;        /* 百分比文字居中 */
    color: #b1cdd4;           /* 百分比文字颜色，与通用QLabel颜色一致 */
    height: 20px;              /* 定义一个合适的高度，可根据需要调整 */
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #48c9b0; /* 使用启动按钮的蓝绿色作为进度块颜色 */
    border-radius: 4px;        /* 进度块的圆角应略小于或等于进度条的圆角 */
    /* 可以为chunk添加一些 margin 来制造一点内边距效果 */
    /* margin: 1px; */
}

QCheckBox::indicator {
    width: 20px; 
    height: 20px;
}
QCheckBox::indicator:unchecked 
{        
    image:  url(:/icons/resource/icon/unchecked.svg);
}
QCheckBox::indicator:checked 
{
    image: url(:/icons/resource/icon/checked.svg)
}
QFrame#operationWindow{
    border: 1px solid #6c7274;
    border-radius: 5px;
}
QFrame#operationWindow QLabel#label_title{
    font-size: 18px;
    font-weight: bold;
    max-height: 20px;
}
QFrame#operationWindow QWidget#widget_1{
    border-bottom-width: 2px;
    border-bottom-style: solid;
}
/* QFrame#operationWindow QWidget#widget_2{
    border-top-width: 2px;
    border-top-style: solid;
} */

/* 用户登录界面美化 */
QWidget#page_login QFrame#frame_login {
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #3a506b, stop: 1 #2c3e50);
    border: 1px solid #556677;
    border-radius: 10px;
    padding: 25px;
    padding-top: 0px;
}
QWidget#page_login QLabel#label_login_title {
    color: #4fc3f7;
    font-size: 22px;
    font-weight: bold;
    background-color: transparent;
    padding: 15px;
    border-bottom: 2px solid #6c7274;
    margin-bottom: 5px;
}

QWidget#page_login QLineEdit {
    border: none;
    border-bottom: 1px solid #6c7274;
    border-radius: 0px;
    font-size: 16px;
    color: #b1cdd4;
    min-height: 30px;
}

QWidget#page_login QLineEdit:focus {
    border-color: #538fc0;
    outline: none;
    color: #ffffff;
}

QWidget#page_login QLabel {
    font-size: 16px;
    font-weight: bold;
}

QWidget#page_login QPushButton#btn_login {
    min-width: 100px;
    min-height: 35px;
}
QFrame#frame_main QPushButton {
    background-color: transparent;
    border-color: transparent;
    min-width: 50px;
    min-height: 50px;
    max-width: 50px;
    max-height: 50px;
}

/* 横向菜单 */
QWidget#widget_horizontalMenu {
	background-color: transparent;
    border-bottom: 1px solid #6c7274;
}
QWidget#widget_horizontalMenu QToolButton {
    border: none;
    border-bottom: 1px solid #6c7274;
	color: #b1cdd4; 
    padding: 5px;
    min-width: 120px;
	min-height: 30px;
    font-size: 12px;
    font-weight: bold;
}
QWidget#widget_horizontalMenu QToolButton:checked {
    border-bottom: 5px solid #2196F3;
    color: #2196F3;  
	font-size: 16px;
    font-weight: bold;
}
QWidget#widget_horizontalMenu QToolButton:hover {
    background-color: #32516d;
	font-size: 16px;
    font-weight: bold;
}

QFrame#frame_task{
    border-bottom: 1px solid #6c7274;
}
QFrame#frame_task QLabel{
    border: 1px solid #6c7274;
    border-radius: 10px;
    font-weight: bold;
    color: #b1cdd4;
}
QFrame#frame_task QLabel#label_title{
    border: none;
}
QFrame#frame_task QPushButton {
    background-color: #3d84a8; /* Indigo 300 - 柔和靛蓝色 */
    border: 1px solid #3d84a8;
    color: #b1cdd4;             /* 白色文字 */
    min-width: 70px;
    min-height: 25px;
    border-radius: 5px;
    font-weight: bold;
}