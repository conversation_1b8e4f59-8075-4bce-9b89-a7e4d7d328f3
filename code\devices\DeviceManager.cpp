#include "DeviceManager.h"

#include <QDebug>

#include "ConfigManager.h"
#include "ConstData.h"
#include "DeviceHelper.h"
#include "DeviceSampleHelper.h"
#include "DeviceSpikeHelper.h"
#include "DeviceWcsHelper.h"
#include "DeviceYclHelper.h"
#include "ModbusManager.h"
#include "define.h"
DeviceManager* DeviceManager::instance()
{
    static DeviceManager instance;
    return &instance;
}

DeviceManager::DeviceManager(/* args */)
{
    // if (!loadConfiguration()) {
    //     // 加载配置失败，使用默认设置
    //     qDebug() << "Failed to load configuration, using default settings";
    // }
    ModbusManager* modbusManager = ModbusManager::instance();
    QStringList deviceLis = modbusManager->getDeviceList();
    for (auto device : deviceLis) {
        switch (DEVICE_MAP[device].deviceType) {
            case DeviceType_normal:
                m_deviceConfigs.insert(device, DeviceConfig());
                m_deviceHelpers.insert(device, new DeviceHelper(device, m_deviceConfigs[device]));
                break;
            case DeviceType_wcs:
                m_deviceHelpers.insert(device, new DeviceWcsHelper(device));
                break;
            case DeviceType_ycl:
                m_deviceHelpers.insert(device, new DeviceYclHelper(device));
                break;
            case DeviceType_spike:
                m_deviceHelpers.insert(device, new DeviceSpikeHelper(device));
                break;
            case DeviceType_sample:
                m_deviceHelpers.insert(device, new DeviceSampleHelper(device));
                break;
            default:
                break;
        }
    }
    for (auto helper : m_deviceHelpers) {
        connect(helper, &IDevice::errorOccurred, this, [this, helper](const QString& errorMessage) {
            emit errorOccurred(helper->getDeviceName(), errorMessage);
        });
        connect(helper, &IDevice::commStatusChanged, this, [this, helper](const bool& commStatus) {
            emit commStatusChanged(helper->getDeviceName(), commStatus);
        });
    }
}

DeviceManager::~DeviceManager()
{
    // saveConfiguration();
    qDeleteAll(m_deviceHelpers);  // 释放所有设备助手对象
}

IDevice* DeviceManager::getDeviceHelper(const QString& deviceName)
{
    if (m_deviceHelpers.contains(deviceName)) {
        return m_deviceHelpers[deviceName];
    }
    return nullptr;
}

void DeviceManager::startAllDataMonitoring()
{
    for (auto helper : m_deviceHelpers) {
        helper->startDataMonitoring();
    }
}

void DeviceManager::stopAllDataMonitoring()
{
    for (auto helper : m_deviceHelpers) {
        helper->stopDataMonitoring();
    }
}

void DeviceManager::stopAllDevices()
{
    for (auto helper : m_deviceHelpers) {
        helper->stop();
    }
}

// bool DeviceManager::saveConfiguration()
// {
//     // 获取配置管理器实例
//     ConfigManager* configMgr = ConfigManager::instance();

//     // 创建设备配置节
//     QJsonObject devicesObj;

//     // 遍历所有设备配置
//     for (auto it = m_deviceConfigs.begin(); it != m_deviceConfigs.end(); ++it) {
//         QString deviceId = it.key();
//         DeviceConfig& config = it.value();

//         QJsonObject deviceObj;
//         deviceObj["name"] = config.name;
//         deviceObj["datetime"] = config.datetime;
//         deviceObj["analyze_lastData"] = config.analyze_lastData;
//         deviceObj["analyze_lastDate"] = config.analyze_lastDate;
//         deviceObj["analyze_upperLimit"] = config.analyze_upperLimit;
//         deviceObj["analyze_current_range"] = config.analyze_current_range;
//         deviceObj["inspect_std_conc"] = config.inspect_std_conc;
//         deviceObj["inspect_conc"] = config.inspect_conc;
//         deviceObj["inspect_datetime"] = config.inspect_datetime;
//         deviceObj["inspect_period_day"] = config.inspect_period_day;
//         deviceObj["inspect_period_hour"] = config.inspect_period_hour;
//         deviceObj["cali_datetime"] = config.cali_datetime;
//         deviceObj["cali_period_week"] = config.cali_period_week;
//         deviceObj["cali_period_hour"] = config.cali_period_hour;

//         // 添加到设备配置节
//         devicesObj[deviceId] = deviceObj;
//     }

//     // 设置配置节
//     configMgr->setSection("devices", devicesObj);

//     // 保存到文件
//     return configMgr->saveConfig();
// }

// bool DeviceManager::loadConfiguration()
// {
//     // 获取配置管理器实例
//     ConfigManager* configMgr = ConfigManager::instance();

//     // 加载配置文件（如果失败不报错，使用默认设置）
//     configMgr->loadConfig();

//     // 获取Modbus设备配置节
//     QJsonObject devicesObj = configMgr->getSection("devices");

//     // 清除现有配置
//     m_deviceConfigs.clear();

//     // 遍历JSON对象的所有键（即设备ID）
//     for (auto it = devicesObj.begin(); it != devicesObj.end(); ++it) {
//         QString deviceId = it.key();
//         QJsonObject deviceObj = it.value().toObject();

//         if (!deviceObj.isEmpty()) {
//             DeviceConfig config;
//             config.name = deviceObj["name"].toString();
//             config.datetime = deviceObj["datetime"].toString();
//             config.analyze_lastData = deviceObj["analyze_lastData"].toDouble(0.0);
//             config.analyze_lastDate = deviceObj["analyze_lastDate"].toString();
//             config.analyze_upperLimit = deviceObj["analyze_upperLimit"].toDouble(0.0);
//             config.analyze_current_range = deviceObj["analyze_current_range"].toDouble(0.0);
//             config.inspect_std_conc = deviceObj["inspect_std_conc"].toDouble(0.0);
//             config.inspect_conc = deviceObj["inspect_conc"].toDouble(0.0);
//             config.inspect_datetime = deviceObj["inspect_datetime"].toString();
//             config.inspect_period_day = deviceObj["inspect_period_day"].toInt(0);
//             config.inspect_period_hour = deviceObj["inspect_period_hour"].toInt(0);
//             config.cali_datetime = deviceObj["cali_datetime"].toString();
//             config.cali_period_week = deviceObj["cali_period_week"].toInt(0);
//             config.cali_period_hour = deviceObj["cali_period_hour"].toInt(0);
//             // 添加到设备配置映射
//             m_deviceConfigs.insert(deviceId, config);
//         }
//     }

//     return true;
// }