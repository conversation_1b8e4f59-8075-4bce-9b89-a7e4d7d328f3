#ifndef DEVICE_WCS_HELPER_H
#define DEVICE_WCS_HELPER_H

#include "IDevice.h"

class DeviceWcsHelper : public IDevice
{
    Q_OBJECT
public:
    DeviceWcsHelper(QString deviceName, QObject* parent = nullptr);
    ~DeviceWcsHelper();
private slots:
    void onGetDeviceData() override;

private:
    static bool m_wcsReadFlag;      // 五参数专用读取标志位
    static int m_currentReadIndex;  // 当前读取的设备索引
};

#endif  // DEVICE_WCS_HELPER_H
