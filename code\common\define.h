#ifndef DEFINE_H
#define DEFINE_H

#include <QDateTime>
#include <QDebug>

#include "sql/errorlog.h"
#include "window/component/notification_manager.h"
#define showWarning(msg)                                   \
    {                                                      \
        NotificationManager::instance()->showWarning(msg); \
        qWarning() << msg;                                 \
    }
#define showInfo(msg)                                   \
    {                                                   \
        NotificationManager::instance()->showInfo(msg); \
        qInfo() << msg;                                 \
    }
#define showSuccess(msg)                                   \
    {                                                      \
        NotificationManager::instance()->showSuccess(msg); \
        qInfo() << msg;                                    \
    }
#define showError(msg, ...)                                                          \
    {                                                                                \
        ErrorLog::insertError(QDateTime::currentDateTime(), QString("%1").arg(msg)); \
        NotificationManager::instance()->showError(msg, ##__VA_ARGS__);              \
        qCritical() << msg;                                                          \
    }
#endif
