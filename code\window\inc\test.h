#ifndef TEST_H
#define TEST_H

#include <QFrame>
#include <QGridLayout>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QList>
#include <QMap>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>

class QResizeEvent;
class System;

namespace Ui {
class test;
}

class test : public QWidget
{
    Q_OBJECT

public:
    explicit test(QWidget* parent = nullptr);
    ~test();

private slots:
    // 统一的原子操作槽函数
    void onAtomicOperationClicked(qint16 operationCode);

    // 复合任务槽函数
    void onCompositeAnalysisClicked();
    void onCompositeSpikeRecoveryClicked();

    // 系统状态槽函数
    void onCurrentTaskChanged(qint16 operateCode);
    void onSystemRunningChanged(bool isRunning);

    // 其他操作
    void onStopCurrentTaskClicked();
    void updateStatus();

private:
    void setupUI();
    void createAtomicOperationButtons();
    void createCompositeTaskButtons();
    void createStatusDisplay();
    void createControlButtons();

    Ui::test* ui;

    // System
    System* m_system;
    qint16 m_currentTaskCode = 0;

    // UI组件
    QGroupBox* m_atomicGroupBox;
    QGroupBox* m_compositeGroupBox;
    QGroupBox* m_statusGroupBox;
    QGroupBox* m_controlGroupBox;

    // 原子操作按钮容器
    QMap<qint16, QPushButton*> m_atomicButtons;

    // 复合任务按钮
    QPushButton* m_btnCompositeAnalysis;
    QPushButton* m_btnCompositeSpikeRecovery;

    // 控制按钮
    QPushButton* m_btnStopCurrentTask;

    // 状态显示标签
    QLabel* m_lblCurrentTask;
    QLabel* m_lblCurrentStep;
    QLabel* m_lblTotalSteps;
    QLabel* m_lblCurrentOperation;
    QLabel* m_lblTaskStatus;
};

#endif  // TEST_H
