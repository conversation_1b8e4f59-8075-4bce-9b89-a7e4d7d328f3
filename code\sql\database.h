#ifndef DATABASE_H
#define DATABASE_H

#include <QDate>
#include <QDateTime>
#include <QDebug>
#include <QMap>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QVariantMap>
#include <QVector>

class Database
{
public:
    static void initTable();

    // 按日期查询数据
    static QVector<QVariantMap> queryByDate(const QDate &date);

    // 获取总记录数
    static int getTotalRecords(const QDate &date = QDate());

    // 分页查询数据
    static QVector<QVariantMap> queryByPage(int page, int pageSize, const QDate &date = QDate());

    // 插入整点数据
    static bool insertHourlyData(const QDateTime &timestamp,
                                 const QMap<QString, double> &deviceValues);

private:
    Database() = delete;
    ~Database() = delete;
    Database(const Database &) = delete;
    Database &operator=(const Database &) = delete;
};

#endif  // DATABASE_H
