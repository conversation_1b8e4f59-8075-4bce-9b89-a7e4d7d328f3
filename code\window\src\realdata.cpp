#include "realdata.h"

#include <QDateTime>

#include "common/ConstData.h"
#include "operationWindow.h"
#include "ui_realdata.h"
realdata::realdata(QWidget *parent) : QWidget(parent), ui(new Ui::realdata)
{
    ui->setupUi(this);
    int i = 0;
    for (const QString &deviceId : DEVICE_NAMES) {
        if (DEVICE_MAP[deviceId].isShowData) {
            CardTemplate *cardTemplate = new CardTemplate(this);
            cardTemplate->setData(deviceId, DEVICE_MAP[deviceId].unit);
            ui->gridLayout->addWidget(cardTemplate, i / 3, i % 3);
            cardTemplates[deviceId] = cardTemplate;
            i++;
        }
    }
}

realdata::~realdata() { delete ui; }
