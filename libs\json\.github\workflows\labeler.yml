name: "Pull Request Labeler"

on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  label:
    permissions:
      contents: read
      pull-requests: write

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - uses: srvaroa/labeler@e216fb40e2e6d3b17d90fb1d950f98bee92f65ce # master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
