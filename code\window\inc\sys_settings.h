#ifndef SYS_SETTINGS_H
#define SYS_SETTINGS_H

#include <QWidget>

#include "appupdater.h"
namespace Ui {
class sys_settings;
}

class sys_settings : public QWidget
{
    Q_OBJECT

public:
    explicit sys_settings(QWidget* parent = nullptr);
    ~sys_settings();

private slots:
    void on_pb_ip_set_clicked();
    void on_pb_serial_set_clicked();
    void on_pb_update_clicked();

private:
    void load_serial_settings_ui();
    void load_serial_settings();
    void load_upload_settings_ui();
    void load_upload_settings();
    void initUpdateSystem();
    void setSaveButtonEnabled(bool enabled);  // 根据用户是否登录设置保存按钮使能
    Ui::sys_settings* ui;
    AppUpdater* m_appUpdater;
    bool m_isDownloading;

protected:
    // 重写showEvent，在页面显示时重新加载配置
    void showEvent(QShowEvent* event) override;
};

#endif  // SYS_SETTINGS_H
