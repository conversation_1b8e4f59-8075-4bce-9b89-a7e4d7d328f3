#ifndef SVGCONTROLLER_H
#define SVGCONTROLLER_H

#include <QByteArray>
#include <QFile>
#include <QFuture>
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QPixmap>
#include <QRegularExpression>
#include <QString>
#include <QSvgRenderer>
#include <QThread>
#include <QtConcurrent>

/**
 * @brief SVG异步渲染线程
 */
class SvgRenderWorker : public QObject
{
    Q_OBJECT

public slots:
    void renderSvg(const QByteArray& svgData, const QSize& size);

signals:
    void renderFinished(const QPixmap& pixmap, const QSize& requestedSize);
    void renderFailed(const QString& error);
};

/**
 * @brief 简化的SVG阀门控制器（单例模式）
 * 用于控制工业流程图中阀门的开关状态显示
 */
class SvgController : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 阀门状态枚举
     */
    enum ValveState {
        VALVE_CLOSED = 0,  // 关闭 - 蓝色
        VALVE_OPEN = 1     // 开启 - 绿色
    };

public:
    /**
     * @brief 获取单例实例
     * @return SvgController单例指针
     */
    static SvgController* getInstance();

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance();

    /**
     * @brief 获取渲染后的QPixmap（同步版本）
     * @param size 输出尺寸，如果为空则使用默认尺寸
     * @return 渲染后的图像
     */
    QPixmap getPixmap(const QSize& size = QSize()) const;

    /**
     * @brief 异步获取渲染后的QPixmap
     * @param size 输出尺寸，如果为空则使用默认尺寸
     * @param requestId 请求ID，用于标识特定请求（可选）
     */
    void getPixmapAsync(const QSize& size = QSize(), const QString& requestId = QString());

    /**
     * @brief 使用 QtConcurrent 异步渲染（推荐）
     * @param size 输出尺寸
     * @return QFuture对象，可用于等待结果
     */
    QFuture<QPixmap> getPixmapConcurrent(const QSize& size = QSize());

    /**
     * @brief 设置阀门状态
     * @param valveId 阀门ID（如："SV1", "SV2"等）
     * @param state 阀门状态（开启/关闭）
     */
    void setValveState(const QString& valveId, ValveState state);

    /**
     * @brief 获取当前阀门状态
     * @param valveId 阀门ID
     * @return 阀门状态
     */
    ValveState getValveState(const QString& valveId) const;

    /**
     * @brief 重置所有阀门到关闭状态
     */
    void resetAllValves();

    /**
     * @brief 获取阀门组件的边界框坐标
     * @param valveId 阀门ID
     * @return 阀门的边界框，如果未找到则返回空矩形
     */
    QRectF getValveBoundingRect(const QString& valveId) const;

    /**
     * @brief 获取阀门组件的中心坐标
     * @param valveId 阀门ID
     * @return 阀门的中心点坐标，如果未找到则返回(-1,-1)
     */
    QPointF getValveCenter(const QString& valveId) const;

    /**
     * @brief 在SVG中添加文本元素
     * @param textId 文本元素的ID
     * @param text 要显示的文本内容
     * @param position 文本位置坐标
     * @param fontSize 字体大小，默认14
     * @param color 文字颜色，默认黑色
     * @param fontFamily 字体族，默认Arial
     * @return 是否添加成功
     */
    bool addTextToSvg(const QString& textId, const QString& text, const QPointF& position,
                      int fontSize = 14, const QString& color = "#000000",
                      const QString& fontFamily = "Arial");

    /**
     * @brief 更新SVG中的文本内容
     * @param textId 文本元素的ID
     * @param newText 新的文本内容
     * @return 是否更新成功
     */
    bool updateTextInSvg(const QString& textId, const QString& newText);

    /**
     * @brief 在指定位置ID处添加文本（基于已有元素位置）
     * @param positionId 位置参考元素的ID（如LB1）
     * @param text 要显示的文本
     * @param offset 相对偏移，默认(0,0)
     * @return 是否添加成功
     */
    bool addTextAtPosition(const QString& positionId, const QString& text,
                           const QPointF& offset = QPointF(0, 0));

    /**
     * @brief 移除SVG中的文本元素
     * @param textId 文本元素的ID
     * @return 是否移除成功
     */
    bool removeTextFromSvg(const QString& textId);

    /**
     * @brief 设置任意组件的颜色
     * @param componentId 组件ID
     * @param color 新颜色（如："#FF0000", "#00FF00"等）
     * @return 是否设置成功
     */
    bool setComponentColor(const QString& componentId, const QString& color);

signals:
    /**
     * @brief 阀门状态改变信号
     * @param valveId 阀门ID
     * @param newState 新状态
     */
    void valveStateChanged(const QString& valveId, ValveState newState);

    /**
     * @brief SVG更新信号
     */
    void svgUpdated();

    /**
     * @brief 异步渲染完成信号
     * @param pixmap 渲染结果
     * @param requestedSize 请求的尺寸
     * @param requestId 请求ID
     */
    void pixmapReady(const QPixmap& pixmap, const QSize& requestedSize, const QString& requestId);

    /**
     * @brief 异步渲染失败信号
     * @param error 错误信息
     * @param requestId 请求ID
     */
    void renderError(const QString& error, const QString& requestId);

private slots:
    void onRenderFinished(const QPixmap& pixmap, const QSize& requestedSize);
    void onRenderFailed(const QString& error);

private:
    // 单例模式：私有构造函数和析构函数
    explicit SvgController(QObject* parent = nullptr);
    ~SvgController();

    // 禁用拷贝构造函数和赋值操作符
    SvgController(const SvgController&) = delete;
    SvgController& operator=(const SvgController&) = delete;

    /**
     * @brief 同步渲染SVG（内部使用）
     * @param svgData SVG数据
     * @param size 渲染尺寸
     * @return 渲染结果
     */
    static QPixmap renderSvgSync(const QByteArray& svgData, const QSize& size);

    /**
     * @brief 加载默认SVG文件
     * @return 是否加载成功
     */
    bool loadDefaultSvgFile();

    /**
     * @brief 更新阀门颜色
     * @param valveId 阀门ID
     * @param color 新颜色
     * @return 是否更新成功
     */
    bool updateValveColor(const QString& valveId, const QString& color);

    /**
     * @brief 获取阀门状态对应的颜色
     * @param state 阀门状态
     * @param componentId 组件ID（用于区分SV和M系列）
     * @return 颜色字符串
     */
    QString getValveStateColor(ValveState state, const QString& componentId = "") const;

private:
    void initCustomComponents();
    void initAsyncRendering();     // 初始化异步渲染
    void cleanupAsyncRendering();  // 清理异步渲染

    static SvgController* m_instance;         // 单例实例
    QSvgRenderer* m_svgRenderer;              // SVG渲染器
    QByteArray m_originalSvgData;             // 原始SVG数据
    QByteArray m_currentSvgData;              // 当前SVG数据
    QMap<QString, ValveState> m_valveStates;  // 阀门状态存储
    QString m_svgFilePath;                    // SVG文件路径

    // 异步渲染相关
    QThread* m_renderThread;          // 渲染线程
    SvgRenderWorker* m_renderWorker;  // 渲染工作对象
    QMutex m_dataMutex;               // 数据保护锁
    QString m_currentRequestId;       // 当前请求ID
};

#endif  // SVGCONTROLLER_H