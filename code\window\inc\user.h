#ifndef USER_H
#define USER_H

#include <QComboBox>
#include <QDialog>
#include <QDialogButtonBox>
#include <QEvent>
#include <QHeaderView>
#include <QInputDialog>
#include <QKeyEvent>
#include <QLabel>
#include <QLineEdit>
#include <QMessageBox>
#include <QPushButton>
#include <QStackedWidget>
#include <QTableWidget>
#include <QWidget>

namespace Ui {
class user;
class UserDialog;
}  // namespace Ui

class UserDialog;

class user : public QWidget
{
    Q_OBJECT

public:
    explicit user(QWidget *parent = nullptr);
    ~user();
    static bool isLoggedIn() { return m_isLoggedIn; };

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

signals:
    void currentUserChanged(const QString &username);

private slots:
    // 登录相关
    void onLoginClicked();
    void onLogoutClicked();

    // 用户管理相关
    void onAddUserClicked();
    void onEditUserClicked();
    void onDeleteUserClicked();
    void onResetPasswordClicked();
    void onRefreshClicked();
    void onTableSelectionChanged();

private:
    Ui::user *ui;
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    // 界面切换
    void switchToLoginPage();
    void switchToManagementPage();

    // 用户管理
    void loadUserList();
    void updateUserButtonStates();
    bool validateLogin(const QString &username, const QString &password);
    void showUserInfo(const QString &username);

    // 当前登录用户信息
    QString m_currentUsername;
    QString m_currentUserRole;
    static bool m_isLoggedIn;
    QTimer *m_timer;
};

// 用户添加/编辑对话框类
class UserDialog : public QDialog
{
    Q_OBJECT

public:
    enum Mode { AddMode, EditMode };

    explicit UserDialog(Mode mode, QWidget *parent = nullptr);
    ~UserDialog();

    void setUserData(const QString &username, const QString &role, bool isActive);
    QString getUsername() const;
    QString getPassword() const;
    QString getRole() const;
    bool getIsActive() const;

private slots:
    void onAccepted();

private:
    Ui::UserDialog *ui;
    Mode m_mode;

    bool validateInput();
};

#endif  // USER_H
