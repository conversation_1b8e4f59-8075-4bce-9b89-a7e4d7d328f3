#ifndef USER_DATABASE_H
#define USER_DATABASE_H

#include <QCryptographicHash>
#include <QDateTime>
#include <QDebug>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QString>
#include <QVariantMap>
#include <QVector>

struct UserInfo {
    int id;
    QString username;
    QString passwordHash;  // 存储密码的MD5哈希值
    QString role;          // admin 或 user
    QString status;        // 启用 或 禁用
    QDateTime createTime;
    QDateTime lastLoginTime;

    UserInfo() : id(-1) {}

    UserInfo(const QString &name, const QString &pwd, const QString &r, const QString &s = "启用")
        : id(-1), username(name), role(r), status(s), createTime(QDateTime::currentDateTime())
    {
        setPassword(pwd);
    }

    void setPassword(const QString &password)
    {
        passwordHash =
            QString(QCryptographicHash::hash(password.toUtf8(), QCryptographicHash::Md5).toHex());
    }

    bool verifyPassword(const QString &password) const
    {
        QString inputHash =
            QString(QCryptographicHash::hash(password.toUtf8(), QCryptographicHash::Md5).toHex());
        return passwordHash == inputHash;
    }

    bool isActive() const { return status == "启用"; }
};

class UserDatabase
{
public:
    // 初始化用户表
    static void initUserTable();

    // 用户验证
    static UserInfo validateUser(const QString &username, const QString &password);

    // 用户管理
    static bool addUser(const UserInfo &userInfo);
    static bool updateUser(const UserInfo &userInfo);
    static bool deleteUser(const QString &username);
    static bool resetPassword(const QString &username, const QString &newPassword);
    static bool updateUserStatus(const QString &username, const QString &status);
    static bool updateLastLoginTime(const QString &username);

    // 查询用户
    static UserInfo getUserByUsername(const QString &username);
    static QVector<UserInfo> getAllUsers();
    static bool userExists(const QString &username);
    static QString getLastLoginUsername();

    // 管理员操作
    static bool isAdmin(const QString &username);
    static int getUserCount();

private:
    // 禁止实例化
    UserDatabase() = delete;
    ~UserDatabase() = delete;
    UserDatabase(const UserDatabase &) = delete;
    UserDatabase &operator=(const UserDatabase &) = delete;

    static QString hashPassword(const QString &password);
    static UserInfo queryToUserInfo(const QSqlQuery &query);
};

#endif  // USER_DATABASE_H