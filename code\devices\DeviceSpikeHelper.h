#ifndef DEVICE_SPIKE_HELPER_H
#define DEVICE_SPIKE_HELPER_H

#include "IDevice.h"

class DeviceSpikeHelper : public IDevice
{
    Q_OBJECT
public:
    DeviceSpikeHelper(QString deviceName, QObject* parent = nullptr);
    ~DeviceSpikeHelper();
    void control(quint16 optCode) override;
    void control(quint16 addr, quint16 data) override;
    void setDeviceTime(const QDateTime& datetime) override;

private slots:
    void onGetDeviceData() override;
};

#endif  // DEVICE_SPIKE_HELPER_H
