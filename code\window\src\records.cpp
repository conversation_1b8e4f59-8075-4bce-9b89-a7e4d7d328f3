#include "records.h"

#include "common/ConstData.h"
#include "ui_records.h"

records::records(QWidget *parent) : QWidget(parent), ui(new Ui::records)
{
    ui->setupUi(this);
    ui->tb_hour->setText("🕐 小时记录");
    ui->tb_upload->setText("🌐 上传记录");
    ui->tb_error->setText("⚠️ 错误记录");

    // 创建菜单按钮组
    menuButtonGroup = new QButtonGroup(this);
    menuButtonGroup->addButton(ui->tb_hour, 1);
    menuButtonGroup->addButton(ui->tb_upload, 2);
    menuButtonGroup->addButton(ui->tb_error, 3);
    connect(menuButtonGroup, &QButtonGroup::idClicked, this, &records::onMenuButtonClicked);

    // 设置表格属性
    ui->tableWidget->setAlternatingRowColors(true);                         // 交替行颜色
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);   // 整行选择
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);  // 单行选择
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);    // 禁止编辑

    // 设置列自动拉伸以填满表格宽度
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);  // 固定第一列
    // 隐藏行号
    ui->tableWidget->verticalHeader()->setVisible(false);

    // 设置行高
    setRowHeightByRecordType(currentRecordType);

    // 计算并设置每页显示的行数
    updatePageSize();
}

records::~records() { delete ui; }

void records::onMenuButtonClicked(int id)
{
    // 转换为记录类型
    RecordType newType = static_cast<RecordType>(id);

    // 如果类型没有改变，直接返回
    if (newType == currentRecordType) {
        return;
    }

    // 更新当前记录类型
    currentRecordType = newType;

    // 根据记录类型设置行高
    setRowHeightByRecordType(newType);

    // 重置到第一页
    currentPage = 1;

    // 更新页面信息并重新加载数据
    updatePageSize();
    updatePageInfo();
    loadPageData();

    qDebug() << "切换到记录类型:" << (id == 1 ? "设备数据" : "上传记录");
}
void records::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 窗口大小改变时重新计算pageSize
    updatePageSize();
    // 重新加载数据
    if (isVisible()) {
        updatePageInfo();
        loadPageData();
    }
}

void records::updatePageSize()
{
    // 获取表格的可视区域高度
    int tableHeight = ui->tableWidget->height();
    // 获取表头高度
    int headerHeight = ui->tableWidget->horizontalHeader()->height();
    // 获取单行高度
    int rowHeight = ui->tableWidget->verticalHeader()->defaultSectionSize();

    // 计算可显示的完整行数
    int visibleRows = (tableHeight - headerHeight) / rowHeight;

    // 设置每页显示行数（至少显示1行）
    pageSize = qMax(1, visibleRows);
}

void records::setRowHeightByRecordType(RecordType type)
{
    int rowHeight = 35;  // 默认行高

    switch (type) {
        case RecordType::DeviceData:
            rowHeight = 35;  // 设备数据记录行高
            break;

        case RecordType::UploadData:
            rowHeight = 65;  // 上传记录行高
            break;

        case RecordType::ErrorData:
            rowHeight = 35;  // 错误记录行高
            break;
    }

    // 设置新的行高
    ui->tableWidget->verticalHeader()->setDefaultSectionSize(rowHeight);
    ui->tableWidget->verticalHeader()->setSectionResizeMode(QHeaderView::Fixed);  // 固定行高
}

void records::loadPageData()
{
    RecordDataHelper::loadPageData(ui->tableWidget, currentRecordType, currentPage, pageSize,
                                   queryDate);
}

void records::updatePageInfo()
{
    // 根据记录类型获取总记录数
    totalRecords = RecordDataHelper::getTotalRecords(currentRecordType, queryDate);

    totalPages = (totalRecords + pageSize - 1) / pageSize;  // 向上取整

    // 确保当前页在有效范围内
    if (currentPage < 1) currentPage = 1;
    if (totalPages == 0) totalPages = 1;  // 避免无数据时除以零
    if (currentPage > totalPages) currentPage = totalPages;

    // 更新按钮状态
    ui->bt_prePage->setEnabled(currentPage > 1);
    ui->bt_nextPage->setEnabled(currentPage < totalPages);
    ui->bt_headPage->setEnabled(currentPage > 1);
    ui->bt_tailPage->setEnabled(currentPage < totalPages);

    // 更新页码显示
    QString pageInfo = QString("%1/%2(%3条)").arg(currentPage).arg(totalPages).arg(totalRecords);
    ui->lb_pageInfo->setText(pageInfo);
}

void records::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    updatePageInfo();
    loadPageData();
}

void records::on_bt_prePage_clicked()
{
    if (currentPage > 1) {
        currentPage--;
        loadPageData();
        updatePageInfo();
    }
}

void records::on_bt_nextPage_clicked()
{
    if (currentPage < totalPages) {
        currentPage++;
        loadPageData();
        updatePageInfo();
    }
}

void records::on_bt_headPage_clicked()
{
    if (currentPage != 1) {
        currentPage = 1;
        loadPageData();
        updatePageInfo();
    }
}

void records::on_bt_tailPage_clicked()
{
    if (currentPage != totalPages) {
        currentPage = totalPages;
        loadPageData();
        updatePageInfo();
    }
}

void records::on_bt_export_clicked() { qDebug() << "导出"; }

void records::on_bt_exec_clicked()
{
    // 获取用户在界面上选择的日期
    queryDate = ui->dateEdit->date();

    // 重置到第一页
    currentPage = 1;

    // 更新页面信息并加载数据
    updatePageInfo();
    loadPageData();
}

void records::on_bt_purge_clicked()
{
    // 重置到第一页
    currentPage = 1;
    queryDate = QDate();  // 重置日期查询条件为无效日期，即查询所有记录
    // 更新页面信息并加载数据
    updatePageInfo();
    loadPageData();
}