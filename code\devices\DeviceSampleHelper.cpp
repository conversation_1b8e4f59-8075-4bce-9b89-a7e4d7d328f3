#include "DeviceSampleHelper.h"

#include <QDebug>

#include "ConstNames.h"
#include "ModbusManager.h"

#define REG_SYSTEM_STATE 0x04         // 系统状态
#define REG_SYSTEM_ERROR 0x32         // 系统错误
#define REG_START_SAMPLING_PUMP 0x05  // 启动采样泵
#define REG_STOP_SAMPLING_PUMP 0x06   // 停止采样泵
#define REG_DRAIN_CONTROL 0x07        // 排空控制

// 采样设备状态定义
typedef enum {
    STATE_STANDBY = 0,            // 待机
    STATE_DRAIN_SAMPLE_LINE = 1,  // 留样管路排空
    STATE_CLEAN_INTAKE = 2,       // 清洗-进水
    STATE_CLEAN_DRAIN = 3,        // 清洗-排水
    STATE_PRE_INTAKE_SAMPLE = 4,  // 预进水-提样
    STATE_SAMPLING = 5,           // 采样
    STATE_DRAIN_PIPELINE = 6,     // 采样管路排空
    STATE_FINISHED = 7,           // 结束
} sample_state_t;

typedef enum {
    CMD_START_PUMP = 1,  // 启动采样泵
    CMD_STOP_PUMP = 2,   // 停止采样泵
    CMD_DRAIN = 3,       // 排空
} sample_cmd_t;

// 操作到寄存器地址的映射
static const QMap<quint16, quint16> SAMPLE_CMD_ADDR_MAP = {
    {CMD_START_PUMP, REG_START_SAMPLING_PUMP},
    {CMD_STOP_PUMP, REG_STOP_SAMPLING_PUMP},
    {CMD_DRAIN, REG_DRAIN_CONTROL},
};

// 操作到控制值的映射
static const QMap<quint16, quint16> SAMPLE_CMD_VALUE_MAP = {
    {CMD_START_PUMP, 1}, {CMD_STOP_PUMP, 1}, {CMD_DRAIN, 0},  // 排空默认值为0（排空所有瓶）
};

static const QMap<QString, quint16> SAMPLE_OPERATE_MAP = {
    {OperationNames::SAMPLE_INTAKE, CMD_START_PUMP},
    {OperationNames::SAMPLE_INTAKE_STOP, CMD_STOP_PUMP},
    {OperationNames::SAMPLE_DRAIN, CMD_DRAIN},
};

static const QMap<quint16, QString> SAMPLE_STATE_MAP = {
    {STATE_STANDBY, StateNames::SAMPLE_STATE_STANDBY},
    {STATE_DRAIN_SAMPLE_LINE, StateNames::SAMPLE_STATE_DRAIN_SAMPLE_LINE},
    {STATE_CLEAN_INTAKE, StateNames::SAMPLE_STATE_CLEAN_INTAKE},
    {STATE_CLEAN_DRAIN, StateNames::SAMPLE_STATE_CLEAN_DRAIN},
    {STATE_PRE_INTAKE_SAMPLE, StateNames::SAMPLE_STATE_PRE_INTAKE_SAMPLE},
    {STATE_SAMPLING, StateNames::SAMPLE_STATE_SAMPLING},
    {STATE_DRAIN_PIPELINE, StateNames::SAMPLE_STATE_DRAIN_PIPELINE},
    {STATE_FINISHED, StateNames::SAMPLE_STATE_FINISHED},
};

typedef enum {
    ERROR_NONE = 0x0000,                  // 无错误
    ERROR_THERMOMETER = 0x0001,           // 位0：温度计故障
    ERROR_FRIDGE_TEMP_OVER = 0x0002,      // 位1：冰箱温度超限
    ERROR_DISTRIBUTOR_ARM = 0x0004,       // 位2：分配臂故障
    ERROR_WATER_INSUFFICIENT = 0x0008,    // 位3：水量不足
    ERROR_REAGENT_INSUFFICIENT = 0x0010,  // 位4：药品不足
    ERROR_BOTTLE_FULL = 0x0020,           // 位5：瓶满
    ERROR_FRIDGE_AUTO_PROTECT = 0x0040,   // 位6：冰箱自动保护
    ERROR_LIFT_ROD = 0x0080,              // 位7：升降杆故障
    ERROR_BOTTLE_DISTRIBUTE = 0x0100,     // 位8：分瓶故障
} sample_error_type_t;

static const QMap<quint16, QString> SAMPLE_ERROR_TYPE_MAP = {
    {ERROR_NONE, ErrorNames::COMMON_ERROR_NONE},
    {ERROR_THERMOMETER, ErrorNames::SAMPLE_ERROR_THERMOMETER},
    {ERROR_FRIDGE_TEMP_OVER, ErrorNames::SAMPLE_ERROR_FRIDGE_TEMP_OVER},
    {ERROR_DISTRIBUTOR_ARM, ErrorNames::SAMPLE_ERROR_DISTRIBUTOR_ARM},
    {ERROR_WATER_INSUFFICIENT, ErrorNames::SAMPLE_ERROR_WATER_INSUFFICIENT},
    {ERROR_REAGENT_INSUFFICIENT, ErrorNames::SAMPLE_ERROR_REAGENT_INSUFFICIENT},
    {ERROR_BOTTLE_FULL, ErrorNames::SAMPLE_ERROR_BOTTLE_FULL},
    {ERROR_FRIDGE_AUTO_PROTECT, ErrorNames::SAMPLE_ERROR_FRIDGE_AUTO_PROTECT},
    {ERROR_LIFT_ROD, ErrorNames::SAMPLE_ERROR_LIFT_ROD},
    {ERROR_BOTTLE_DISTRIBUTE, ErrorNames::SAMPLE_ERROR_BOTTLE_DISTRIBUTE},
};

DeviceSampleHelper::DeviceSampleHelper(QString deviceName, QObject* parent)
    : IDevice(deviceName, parent)
{
    m_deviceType = DeviceType_sample;
    m_operateMap = SAMPLE_OPERATE_MAP;
    m_statusMap = SAMPLE_STATE_MAP;
    m_errorMap = SAMPLE_ERROR_TYPE_MAP;
    qDebug() << "DeviceSampleHelper initialized with device name:" << m_deviceName;
}

DeviceSampleHelper::~DeviceSampleHelper()
{
    qDebug() << "DeviceSampleHelper destroyed for device name:" << m_deviceName;
}

void DeviceSampleHelper::control(quint16 addr, quint16 data)
{
    if (m_busyFlags.controlBusy) {
        return;
    }
    m_busyFlags.controlBusy = true;
    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.controlBusy = false;
        return;
    }
    QVector<quint16> _data;
    _data << data;
    modbus->asyncWrite(addr, _data)
        .fail([this](const QString& error) {
            qDebug() << "Failed to write register for device:" << m_deviceName << "Error:" << error;
        })
        .finally([this]() { m_busyFlags.controlBusy = false; });
}

void DeviceSampleHelper::control(quint16 optCode)
{
    quint16 addr = SAMPLE_CMD_ADDR_MAP.value(optCode, 0);
    quint16 data = SAMPLE_CMD_VALUE_MAP.value(optCode, 0);
    control(addr, data);
}

void DeviceSampleHelper::onGetDeviceData()
{
    if (m_busyFlags.dataBusy) {
        return;
    }
    m_busyFlags.dataBusy = true;

    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.dataBusy = false;
        return;
    }
    modbus->asyncRead(REG_SYSTEM_STATE, 1)
        .then([this, modbus](const QVector<quint16>& result) {
            m_deviceData.status = result[0];
            m_isIdle = m_deviceData.status == STATE_STANDBY;
            return modbus->asyncRead(REG_SYSTEM_ERROR, 1);
        })
        .then([this, modbus](const QVector<quint16>& result) {
            m_deviceData.faultCode = result[0];
            if (m_deviceData.faultCode != m_lastFaultCode && m_deviceData.faultCode != ERROR_NONE) {
                emit errorOccurred(QString("错误: %1").arg(getErrorName(m_deviceData.faultCode)));
            }
            m_lastFaultCode = m_deviceData.faultCode;
            emit deviceDataChanged(m_deviceData);
        })
        .finally([this]() { m_busyFlags.dataBusy = false; });
}