#ifndef PARA_SETTINGS_H
#define PARA_SETTINGS_H

#include <QMap>
#include <QPushButton>
#include <QShowEvent>
#include <QSpinBox>
#include <QWidget>
namespace Ui {
class para_settings;
}

class para_settings : public QWidget
{
    Q_OBJECT

public:
    explicit para_settings(QWidget *parent = nullptr);
    ~para_settings();

private slots:
    void on_tb_meter_clicked();
    void on_tb_cs_clicked();
    void on_pb_period_sava_clicked();

private:
    void showEvent(QShowEvent *event) override;
    void init_hour_buttons();
    void init_quality_spinbox();
    void initMappings();
    void setSaveButtonEnabled(bool enabled);
    Ui::para_settings *ui;
    QList<QPushButton *> m_hourButtons;
    QMap<QString, QSpinBox *> m_spinBoxMap;
};

#endif  // PARA_SETTINGS_H
