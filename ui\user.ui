<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>user</class>
 <widget class="QWidget" name="user">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户管理</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_login">
      <layout class="QHBoxLayout" name="horizontalLayout_login">
       <item>
        <spacer name="horizontalSpacer_left">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QFrame" name="frame_login">
         <property name="minimumSize">
          <size>
           <width>400</width>
           <height>300</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>400</width>
           <height>300</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_login">
          <property name="bottomMargin">
           <number>25</number>
          </property>
          <item>
           <widget class="QLabel" name="label_login_title">
            <property name="text">
             <string>用户登录</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_title">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>15</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout">
            <item row="1" column="0">
             <widget class="QLineEdit" name="lineEdit_password">
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="echoMode">
               <enum>QLineEdit::Password</enum>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="placeholderText">
               <string>请输入密码</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLineEdit" name="lineEdit_username">
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="placeholderText">
               <string>请输入用户名</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_middle">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_login_buttons">
            <item>
             <spacer name="horizontalSpacer_login_left">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="btn_login">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>35</height>
               </size>
              </property>
              <property name="text">
               <string>登录</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_login_right">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_right">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_management">
      <layout class="QVBoxLayout" name="verticalLayout_management">
       <item>
        <widget class="QGroupBox" name="groupBox_toolbar">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>80</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>100</height>
          </size>
         </property>
         <property name="title">
          <string>用户操作</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_toolbar">
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>9</number>
          </property>
          <item>
           <widget class="QLabel" name="label_current_user">
            <property name="styleSheet">
             <string notr="true">font-weight: bold; color: #5d9cec; font-size: 15px;</string>
            </property>
            <property name="text">
             <string>当前用户: </string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_toolbar">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="btn_add_user">
            <property name="text">
             <string>添加用户</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_refresh">
            <property name="text">
             <string>刷新</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_logout">
            <property name="text">
             <string>退出登录</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_userlist">
         <property name="title">
          <string>用户列表</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_userlist">
          <property name="topMargin">
           <number>20</number>
          </property>
          <item>
           <widget class="QTableWidget" name="tableWidget_users">
            <property name="alternatingRowColors">
             <bool>true</bool>
            </property>
            <property name="selectionBehavior">
             <enum>QAbstractItemView::SelectRows</enum>
            </property>
            <property name="showGrid">
             <bool>false</bool>
            </property>
            <property name="sortingEnabled">
             <bool>true</bool>
            </property>
            <attribute name="horizontalHeaderVisible">
             <bool>true</bool>
            </attribute>
            <attribute name="verticalHeaderVisible">
             <bool>false</bool>
            </attribute>
            <column>
             <property name="text">
              <string>ID</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>用户名</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>角色</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>创建时间</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>最后登录</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>状态</string>
             </property>
            </column>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_table_buttons">
            <item>
             <spacer name="horizontalSpacer_table">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="btn_edit">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>编辑用户</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btn_delete">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>删除用户</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btn_reset_password">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>重置密码</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>lineEdit_username</tabstop>
  <tabstop>lineEdit_password</tabstop>
  <tabstop>btn_login</tabstop>
  <tabstop>btn_add_user</tabstop>
  <tabstop>btn_refresh</tabstop>
  <tabstop>btn_logout</tabstop>
  <tabstop>tableWidget_users</tabstop>
  <tabstop>btn_edit</tabstop>
  <tabstop>btn_delete</tabstop>
  <tabstop>btn_reset_password</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
