#include "IDevice.h"

#include <QTimer>

#include "ModbusManager.h"
#include "common/define.h"

IDevice::IDevice(const QString& deviceName, QObject* parent)
    : QObject(parent), m_deviceName(deviceName), m_deviceDataTimer(nullptr)
{
    // 初始化定时器
    m_deviceDataTimer = new QTimer(this);
    connect(m_deviceDataTimer, &QTimer::timeout, this, [this]() {
        if (isEnabled()) {
            onGetDeviceData();
        }
    });
    m_deviceDataTimer->setInterval(3000);

    // 连接ModbusManager信号
    connect(ModbusManager::instance(), &ModbusManager::deviceError, this,
            [this](const QString& deviceId, const QString& errorMessage) {
                if (deviceId == m_deviceName) {
                    emit commStatusChanged(false);
                }
            });

    connect(ModbusManager::instance(), &ModbusManager::deviceCommSuccess, this,
            [this](const QString& deviceId) {
                if (deviceId == m_deviceName) {
                    emit commStatusChanged(true);
                }
            });
}

bool IDevice::isEnabled() { return ModbusManager::instance()->isDeviceEnabled(m_deviceName); }

void IDevice::startDataMonitoring()
{
    if (isEnabled()) {
        onGetDeviceData();
        if (m_deviceDataTimer && !m_deviceDataTimer->isActive()) {
            qDebug() << m_deviceName << ":startDataMonitoring";
            m_deviceDataTimer->start();
        }
    }
}

void IDevice::stopDataMonitoring()
{
    if (m_deviceDataTimer && m_deviceDataTimer->isActive() && isIdle()) {
        qDebug() << m_deviceName << ":stopDataMonitoring";
        m_deviceDataTimer->stop();
    }
}