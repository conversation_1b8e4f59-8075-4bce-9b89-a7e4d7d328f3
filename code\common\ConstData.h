#ifndef CONSTDATA_H
#define CONSTDATA_H

#include <QMap>
#include <QObject>
#include <QString>
#include <QStringList>
enum DeviceType {
    DeviceType_normal = 1,  // 常规设备
    DeviceType_wcs,         // 五参数设备
    DeviceType_ycl,         // 预处理设备
    DeviceType_Other,       // 其他设备
    DeviceType_spike,       // 加标设备
    DeviceType_sample,      // 留样单元
};
struct DeviceAttribute {
    QString unit;
    bool isShowData;
    bool isOperable;
    bool isTimeSync;
    DeviceType deviceType;
    QString encoding;
};
namespace DeviceNames {
extern const QString NORMAL_MNO2;
extern const QString NORMAL_NH3N;
extern const QString NORMAL_TP;
extern const QString NORMAL_TN;
extern const QString WCS_TEMP;
extern const QString WCS_PH;
extern const QString WCS_DO;
extern const QString WCS_TURB;
extern const QString WCS_COND;
extern const QString OTHER_BIO;
extern const QString OTHER_SAMPLE;
extern const QString PRE_PROCESS;
extern const QString SPIKE_MNO2;
extern const QString SPIKE_NH3N;
extern const QString SPIKE_TP;
extern const QString SPIKE_TN;
// 根据类型获取设备名称
extern const QStringList getDeviceNamesByType(DeviceType type);
}  // namespace DeviceNames
extern const QStringList DEVICE_NAMES;                   // 设备名称列表
extern const QMap<QString, DeviceAttribute> DEVICE_MAP;  // 设备属性列表

#endif  // CONSTDATA_H