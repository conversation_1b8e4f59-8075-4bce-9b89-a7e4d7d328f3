#include "ConstData.h"
struct _DeviceData {
    QString name;
    DeviceAttribute attribute;
};

// clang-format off
namespace DeviceNames {
    const QString NORMAL_MNO2           = "高锰酸盐";
    const QString NORMAL_NH3N           = "氨氮";
    const QString NORMAL_TP             = "总磷";
    const QString NORMAL_TN             = "总氮";
    const QString WCS_TEMP              = "水温";
    const QString WCS_PH                = "PH";
    const QString WCS_DO                = "溶解氧";
    const QString WCS_TURB              = "浊度";
    const QString WCS_COND              = "电导率";
    const QString OTHER_BIO             = "生物毒性";
    const QString OTHER_SAMPLE          = "留样单元";
    const QString PRE_PROCESS           = "预处理";
    const QString SPIKE_MNO2            = "高锰加标";
    const QString SPIKE_NH3N            = "氨氮加标";
    const QString SPIKE_TP              = "总磷加标";
    const QString SPIKE_TN              = "总氮加标";
}  // namespace DeviceNames

static const QList<_DeviceData> _DEVICE_DATA_LIST = {
    // 设备名称                      单位   是否显示数据  是否可操作    是否有时间同步         设备类型             设备编码
    { DeviceNames::NORMAL_MNO2,  { "mg/L",   true,        true,         true,         DeviceType_normal,       "w01019" } },
    { DeviceNames::NORMAL_NH3N,  { "mg/L",   true,        true,         true,         DeviceType_normal,       "w21003" } },
    { DeviceNames::NORMAL_TP,    { "mg/L",   true,        true,         true,         DeviceType_normal,       "w21011" } },
    { DeviceNames::NORMAL_TN,    { "mg/L",   true,        true,         true,         DeviceType_normal,       "w21001" } },
    { DeviceNames::WCS_TEMP,     { "℃",     true,        false,        false,        DeviceType_wcs,          "w01010" } },
    { DeviceNames::WCS_PH,       { "pH",     true,        false,        false,        DeviceType_wcs,          "w01001" } },
    { DeviceNames::WCS_DO,       { "mg/L",   true,        false,        false,        DeviceType_wcs,          "w01009" } },
    { DeviceNames::WCS_TURB,     { "NTU",    true,        false,        false,        DeviceType_wcs,          "w01003" } },
    { DeviceNames::WCS_COND,     { "mS/cm",  true,        false,        false,        DeviceType_wcs,          "w01014" } },
    { DeviceNames::OTHER_BIO,    { "%",      true,        false,        false,        DeviceType_Other,        "w01023" } },
    { DeviceNames::PRE_PROCESS,  { "",       false,       true,         false,        DeviceType_ycl,          ""       } },
    { DeviceNames::SPIKE_MNO2,   { "",       false,       true,         true,         DeviceType_spike,        ""       } },
    { DeviceNames::SPIKE_NH3N,   { "",       false,       true,         true,         DeviceType_spike,        ""       } },
    { DeviceNames::SPIKE_TP,     { "",       false,       true,         true,         DeviceType_spike,        ""       } },
    { DeviceNames::SPIKE_TN,     { "",       false,       true,         true,         DeviceType_spike,        ""       } },
    { DeviceNames::OTHER_SAMPLE, { "",       false,       true,         false,        DeviceType_sample,       ""       } },
};
// clang-format on
namespace DeviceNames {
const QStringList getDeviceNamesByType(DeviceType type)
{
    QStringList names;
    for (const auto& data : _DEVICE_DATA_LIST) {
        if (data.attribute.deviceType == type) {
            names << data.name;
        }
    }
    return names;
}
}  // namespace DeviceNames
const QStringList DEVICE_NAMES = []() {
    QStringList names;
    for (const auto& data : _DEVICE_DATA_LIST) {
        names << data.name;
    }
    return names;
}();
const QMap<QString, DeviceAttribute> DEVICE_MAP = []() {
    QMap<QString, DeviceAttribute> map;
    for (const auto& data : _DEVICE_DATA_LIST) {
        map[data.name] = {data.attribute};
    }
    return map;
}();
