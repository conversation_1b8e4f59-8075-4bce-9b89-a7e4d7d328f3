#ifndef MODBUSMANAGER_H
#define MODBUSMANAGER_H

#include <QMap>
#include <QMutex>
#include <QObject>
#include <QRecursiveMutex>
#include <QString>

#include "ModbusHelper.h"

// 设备配置结构体
struct MbDeviceConfig {
    QString name;      // 设备名称
    QString portName;  // 串口名或IP地址
    int baudRate;      // 波特率(仅RTU)
    int slaveAddress;  // 从站地址/单元ID
    int port;          // TCP端口(仅TCP)
    bool isTCP;        // 是否TCP连接
    bool enabled;      // 是否启用

    // 私有构造函数，由工厂方法调用
    MbDeviceConfig(const QString& _name, const QString& _addr, int _param1, int _param2,
                   bool _isTcp)
        : name(_name),
          portName(_addr),
          baudRate(_isTcp ? 0 : _param1),
          slaveAddress(_isTcp ? _param2 : _param1),
          port(_isTcp ? _param1 : 0),
          isTCP(_isTcp),
          enabled(false)
    {
    }

    // 静态工厂方法 - RTU模式
    static MbDeviceConfig createRTU(const QString& name, const QString& portName, int baudRate,
                                    int slaveAddress)
    {
        return MbDeviceConfig(name, portName, baudRate, slaveAddress, false);
    }

    // 静态工厂方法 - TCP模式
    static MbDeviceConfig createTCP(const QString& name, const QString& ipAddress, int port,
                                    int unitId)
    {
        return MbDeviceConfig(name, ipAddress, port, unitId, true);
    }
    // 默认构造函数
    MbDeviceConfig()
        : name("default"),
          portName("default"),
          baudRate(9600),
          slaveAddress(1),
          port(502),
          isTCP(false),
          enabled(false)
    {
    }
};

class ModbusManager : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static ModbusManager* instance();

    // 释放单例实例 (应用程序退出时调用)
    static void destroy();

    // 添加设备配置
    void addDevice(const QString& deviceId, const MbDeviceConfig& config);

    // 移除设备配置
    void removeDevice(const QString& deviceId);

    // 启用设备
    bool enableDevice(const QString& deviceId);

    // 禁用设备
    void disableDevice(const QString& deviceId);

    // 更新设备配置
    void updateDeviceConfig(const QString& deviceId, const MbDeviceConfig& config);

    // 获取指定设备的ModbusHelper
    ModbusHelper* getModbusHelper(const QString& deviceId);

    // 检查设备是否启用
    bool isDeviceEnabled(const QString& deviceId) const;

    // 获取所有设备ID列表
    QStringList getDeviceList() const;

    // 获取设备配置
    MbDeviceConfig getDeviceConfig(const QString& deviceId) const;

    // 保存配置
    bool saveConfiguration();

    // 加载配置
    bool loadConfiguration();

signals:
    // 设备连接状态变化信号
    void deviceStatusChanged(const QString& deviceId, bool connected);
    // 设备通讯成功
    void deviceCommSuccess(const QString& deviceId);
    // 设备错误信号
    void deviceError(const QString& deviceId, const QString& errorMessage);

private slots:
    // 处理设备错误
    void onDeviceError(const QString& deviceId);

private:
    // 私有构造函数和析构函数（单例模式）
    explicit ModbusManager(QObject* parent = nullptr);
    ~ModbusManager();

    // 禁止拷贝和赋值（单例模式）
    ModbusManager(const ModbusManager&) = delete;
    ModbusManager& operator=(const ModbusManager&) = delete;

    // 单例实例
    static ModbusManager* m_instance;

    // 设备配置映射
    QMap<QString, MbDeviceConfig> m_deviceConfigs;

    // 活动的Modbus设备映射
    QMap<QString, ModbusHelper*> m_activeDevices;

    // 互斥锁改为递归互斥锁，允许同一线程多次锁定
    mutable QRecursiveMutex m_mutex;

    // 单例互斥锁也改为递归互斥锁
    static QRecursiveMutex m_instanceMutex;

    // 创建Modbus连接
    ModbusHelper* createModbusConnection(const MbDeviceConfig& config);
};

#endif  // MODBUSMANAGER_H