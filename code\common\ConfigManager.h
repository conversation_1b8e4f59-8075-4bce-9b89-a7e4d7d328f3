#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QDir>
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMap>
#include <QObject>
#include <QString>
#include <QVariant>

/**
 * @brief 配置管理类，提供统一的JSON配置读写功能
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取配置管理器单例
     * @return 配置管理器实例
     */
    static ConfigManager* instance();

    /**
     * @brief 设置应用配置目录
     * @param path 配置目录路径
     */
    void setConfigDir(const QString& path);

    /**
     * @brief 设置配置文件名
     * @param filename 配置文件名（不包含路径）
     */
    void setConfigFilename(const QString& filename);

    /**
     * @brief 获取配置目录
     * @return 配置目录路径
     */
    QString configDir() const;

    /**
     * @brief 获取配置文件完整路径
     * @return 配置文件完整路径
     */
    QString configFilePath() const;

    /**
     * @brief 加载配置
     * @return 成功返回true，失败返回false
     */
    bool loadConfig();

    /**
     * @brief 保存配置
     * @return 成功返回true，失败返回false
     */
    bool saveConfig();

    /**
     * @brief 获取整个配置节
     * @param section 配置节名称
     * @return 指定配置节的JSON对象
     */
    QJsonObject getSection(const QString& section) const;

    /**
     * @brief 设置整个配置节
     * @param section 配置节名称
     * @param data 配置数据
     */
    void setSection(const QString& section, const QJsonObject& data);

    /**
     * @brief 获取配置值
     * @param section 配置节名称
     * @param key 配置键名
     * @param defaultValue 默认值
     * @return 配置值，如不存在则返回默认值
     */
    QVariant getValue(const QString& section, const QString& key,
                      const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief 设置配置值
     * @param section 配置节名称
     * @param key 配置键名
     * @param value 配置值
     */
    void setValue(const QString& section, const QString& key, const QVariant& value);

    /**
     * @brief 判断配置节是否存在
     * @param section 配置节名称
     * @return 存在返回true，不存在返回false
     */
    bool hasSection(const QString& section) const;

    /**
     * @brief 判断配置键是否存在
     * @param section 配置节名称
     * @param key 配置键名
     * @return 存在返回true，不存在返回false
     */
    bool hasKey(const QString& section, const QString& key) const;

    /**
     * @brief 移除配置节
     * @param section 配置节名称
     */
    void removeSection(const QString& section);

    /**
     * @brief 移除配置键
     * @param section 配置节名称
     * @param key 配置键名
     */
    void removeKey(const QString& section, const QString& key);

    /**
     * @brief 获取所有配置节名称
     * @return 配置节名称列表
     */
    QStringList getSections() const;

signals:
    /**
     * @brief 配置变更信号
     * @param section 发生变更的配置节
     */
    void configChanged(const QString& section);

private:
    explicit ConfigManager(QObject* parent = nullptr);
    ~ConfigManager();

    // 禁止拷贝
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    // 单例实例
    static ConfigManager* m_instance;

    // 根配置对象
    QJsonObject m_rootConfig;

    // 配置目录
    QString m_configDir;

    // 配置文件名
    QString m_configFilename;

    // 配置是否已修改
    bool m_modified;

    // 确保配置目录存在
    bool ensureConfigDirExists() const;
};

#endif  // CONFIGMANAGER_H