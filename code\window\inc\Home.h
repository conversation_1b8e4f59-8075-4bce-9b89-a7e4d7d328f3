#ifndef HOME_H
#define HOME_H

#include <QFrame>
#include <QLabel>
#include <QList>
#include <QPushButton>
#include <QWidget>

#include "DeviceManager.h"
#include "common/SvgController.h"
#include "operationWindow.h"

class QResizeEvent;

namespace Ui {
class Home;
}

// 存储控件的相对位置信息
struct WidgetInfo {
    QWidget* widget;     // 控件指针
    double relativeX;    // 相对X位置 (0.0-1.0)
    double relativeY;    // 相对Y位置 (0.0-1.0)
    QSize originalSize;  // 原始大小
    QString name;        // 控件名称（用于调试）
};

class Home : public QWidget
{
    Q_OBJECT

public:
    explicit Home(QWidget* parent = nullptr);
    ~Home();

protected:
    void resizeEvent(QResizeEvent* event) override;
    void showEvent(QShowEvent* event) override;
    void hideEvent(QHideEvent* event) override;

private slots:
    void updateSvgDisplay();
    void updateSvgDisplaySync();  // 同步更新SVG显示
    void onValveButtonClicked();
    void onPixmapReady(const QPixmap& pixmap, const QSize& requestedSize, const QString& requestId);
    void onRenderError(const QString& error, const QString& requestId);
    void on_pb_ycl_opt_clicked();
    void on_pb_single_start_clicked();

private:
    void initInfoLabel();
    void initSvgDisplay();
    void initConnections();
    void createValveButtons();  // 创建阀门按钮
    void clearValveButtons();   // 清理阀门按钮
    void updateSystemState(const quint16& status);
    void updateValveState(const QVector<quint16>& valveStatus);
    void updateDeviceData(IDevice* device, int id, const DeviceData& data);
    void startDataMonitoring();
    void stopDataMonitoring();

    Ui::Home* ui;
    QList<WidgetInfo> widgetInfos;
    QSize frameOriginalSize;  // QFrame的原始大小

    // SVG相关成员
    SvgController* m_svgController;
    QLabel* m_svgLabel;

    // 阀门按钮容器
    QList<QPushButton*> m_valveButtons;
};

#endif  // HOME_H
