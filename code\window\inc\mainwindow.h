#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QDebug>
#include <QDirIterator>
#include <QFile>
#include <QListView>
#include <QMainWindow>
#include <QMessageBox>
#include <QStackedWidget>
#include <QTextStream>

#include "Home.h"
#include "notification_manager.h"
#include "operate.h"
#include "para_settings.h"
#include "realdata.h"
#include "records.h"
#include "sys_settings.h"
#include "test.h"
#include "ui_mainwindow.h"
#include "user.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onMenuButtonClicked(int id);
    void on_bt_exit_clicked();

    void on_startButton_clicked();

    void on_stopButton_clicked();

private:
    Ui_MainWindow *ui;
    QButtonGroup *menuButtonGroup;             // 菜单按钮组
    void loadStyleSheet(const QString &path);  // 添加加载样式表的函数
    void initNotificationSystem();             // 初始化通知系统
    void initDevicesShowError();               // 初始化设备错误显示
};

#endif  // TEST_H
