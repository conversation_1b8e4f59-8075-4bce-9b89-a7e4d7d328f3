#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H
#include <QObject>

#include "DeviceHelper.h"

class DeviceManager : public QObject
{
    Q_OBJECT
public:
    static DeviceManager* instance();
    IDevice* getDeviceHelper(const QString& deviceName);
    void startAllDataMonitoring();
    void stopAllDataMonitoring();
    void stopAllDevices();
    // // 保存配置
    // bool saveConfiguration();
    // // 加载配置
    // bool loadConfiguration();

private:
    explicit DeviceManager(/* args */);
    ~DeviceManager();
    // 禁止拷贝和赋值
    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;

    QMap<QString, DeviceConfig> m_deviceConfigs;  // 设备配置列表，key为设备ID，value为设备配置对象
    QMap<QString, IDevice*> m_deviceHelpers;      // 设备助手列表，key为设备ID，value为设备助手对象
signals:
    void errorOccurred(const QString& deviceName, const QString& errorMessage);
    void commStatusChanged(const QString& deviceName, bool commStatus);
};

#endif  // DEVICE_MANAGER_H
