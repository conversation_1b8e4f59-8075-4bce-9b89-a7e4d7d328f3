#ifndef SYSTEM_SCHEDULER_H
#define SYSTEM_SCHEDULER_H

#include <QDateTime>
#include <QJsonObject>
#include <QObject>
#include <QTimer>

#include "SystemConfig.h"

// 前向声明
class TaskStateMachine;

// 系统信息结构体
struct SystemInfo {
    QDateTime nextAnalysisTime;       // 下次水样测量时间
    QDateTime nextZeroCheckTime;      // 下次零点核查时间
    QDateTime nextStdCheckTime;       // 下次标液核查时间
    QDateTime nextCalibTime;          // 下次校正标定时间
    QDateTime nextSpikeRecoveryTime;  // 下次加标回收时间
    QDateTime nextCleanTime;          // 下次清洗时间

    // 序列化方法
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
};

/**
 * @brief 系统调度器类
 * 负责定时任务的调度和执行
 */
class SystemScheduler : public QObject
{
    Q_OBJECT

public:
    explicit SystemScheduler(QObject* parent = nullptr);
    ~SystemScheduler();

    // 调度控制
    void start();
    void stop();
    bool isRunning() const { return m_isRunning; }

    // 信息获取
    SystemInfo getInfo() const { return m_info; }
    QString getTaskName(qint16 operateCode) const;
    // 时间计算
    void updateNextTimes(const SystemConfig& config);

    // 手动执行任务
    void executeTask(qint16 operateCode);

    // SystemInfo持久化存储
    bool loadSystemInfo();
    bool saveSystemInfo();

signals:
    void infoChanged(const SystemInfo& info);
    void isRunningChanged(bool isRunning);
    void taskExecuted(qint16 operateCode);
    void currentTaskChanged(qint16 operateCode);
    void taskFailed(qint16 operateCode, const QString& reason);  // 任务失败信号

private slots:
    void checkScheduled();
    void onTaskTimer();  // 监控所有设备状态

private:
    void hourHandler(QDateTime currentDateTime);
    void calculateNextTime(const SystemConfig& config);
    void insertHourlyData();
    void updateExpiredTimes(const SystemConfig& config);
    QDateTime calculateAnalysisTime(const SystemConfig& config) const;
    QDateTime calculateIntervalBasedTime(int intervalDays, int timeHour) const;

    QTimer* m_timer;
    QTimer* m_taskTimer;
    SystemInfo m_info;
    bool m_isRunning = false;
    SystemConfig m_currentConfig;
    int m_currentTaskCode = 0;
    TaskStateMachine* m_taskStateMachine;
};

#endif  // SYSTEM_SCHEDULER_H