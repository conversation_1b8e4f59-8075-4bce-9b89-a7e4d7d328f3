#ifndef DEVICE_YCL_HELPER_H
#define DEVICE_YCL_HELPER_H

#include "IDevice.h"
// 子状态定义
typedef enum {
    VALVE_OP_IDLE = 0,     // 空闲状态
    VALVE_OP_DRAW_WATER,   // 单步抽水
    VALVE_OP_UPPER_WATER,  // 单步配水
    VALVE_OP_EMPTY_WATER,  // 单步排空
    VALVE_OP_CLEAN,        // 单步清洗
    VALVE_OP_CLEAN_ALGAE,  // 抽除藻剂
    VALVE_OP_SETTLE,       // 等待沉淀
} ycl_step_state_t;
class DeviceYclHelper : public IDevice
{
    Q_OBJECT
public:
    DeviceYclHelper(QString deviceName, QObject* parent = nullptr);
    ~DeviceYclHelper();
    void control(quint16 optCode) override;
    void control(quint16 addr, quint16 data) override;
private slots:
    void onGetDeviceData() override;
};
#endif  // DEVICE_YCL_HELPER_H
