#include "DeviceHelper.h"

#include <QDebug>

#include "ConstData.h"
#include "ConstNames.h"

// 常规设备命令
enum DeviceOperate {
    DEVICE_ANALYSIS = 199,    // 水样测量
    DEVICE_ZERO_CHECK = 356,  // 零点核查
    DEVICE_STD_CHECK = 208,   // 标液核查
    DEVICE_CALIB = 355,       // 校正标定
    DEVICE_CALIB_1 = 197,     // 校正标一
    DEVICE_CALIB_2 = 198,     // 校正标二
    DEVICE_CLEAN = 207,       // 仪表清洗
    DEVICE_STOP = 209,        // 停止设备
    DEVICE_SET_TIME = 200,    // 确定校时
};
static const QMap<QString, quint16> DEVICE_OPERATE_MAP = {
    {OperationNames::NORMAL_ANALYSIS, DEVICE_ANALYSIS},
    {OperationNames::NORMAL_ZERO_CHECK, DEVICE_ZERO_CHECK},
    {OperationNames::NORMAL_STD_CHECK, DEVICE_STD_CHECK},
    {OperationNames::NORMAL_CALIB_FULL, DEVICE_CALIB},
    {OperationNames::NORMAL_CALIB_1, DEVICE_CALIB_1},
    {OperationNames::NORMAL_CALIB_2, DEVICE_CALIB_2},
    {OperationNames::NORMAL_CLEAN, DEVICE_CLEAN},
    {OperationNames::COMMON_STOP, DEVICE_STOP},
    {OperationNames::COMMON_SET_TIME, DEVICE_SET_TIME},
};

enum DeviceStatus {
    DEVICE_STATUS_RUNNING = 0,      // 运行
    DEVICE_STATUS_MAINTENANCE = 1,  // 维护
    DEVICE_STATUS_FAULT = 2,        // 故障
    DEVICE_STATUS_CALIB = 3,        // 校准
    DEVICE_STATUS_INSPECT = 4,      // 核查
    DEVICE_STATUS_ANALYSIS = 6,     // 测量
    DEVICE_STATUS_CALIBRATION = 7,  // 标定
    DEVICE_STATUS_IDLE = 8,         // 空闲
    DEVICE_STATUS_OPERATION = 9,    // 运维
    DEVICE_STATUS_OTHER = 10,       // 其他
};

// TODO 组态校正状态变量StateManualCalib需要改成5
// 组态需要添加DTStartCalibCmd校正命令，零点核查未实现，标一标二状态没有
static const QMap<quint16, QString> DEVICE_STATUS_MAP = {
    {DEVICE_STATUS_RUNNING, StateNames::NORMAL_STATE_RUNNING},
    {DEVICE_STATUS_MAINTENANCE, StateNames::NORMAL_STATE_MAINTENANCE},
    {DEVICE_STATUS_FAULT, StateNames::NORMAL_STATE_FAULT},
    {DEVICE_STATUS_CALIB, StateNames::NORMAL_STATE_CALIB},
    {DEVICE_STATUS_INSPECT, StateNames::NORMAL_STATE_INSPECT},
    {DEVICE_STATUS_ANALYSIS, StateNames::NORMAL_STATE_ANALYSIS},
    {DEVICE_STATUS_CALIBRATION, StateNames::NORMAL_STATE_CALIBRATION},
    {DEVICE_STATUS_IDLE, StateNames::NORMAL_STATE_IDLE},
    {DEVICE_STATUS_OPERATION, StateNames::NORMAL_STATE_OPERATION},
    {DEVICE_STATUS_OTHER, StateNames::NORMAL_STATE_OTHER},
};
/*
常规设备故障码
    0：无报警
    1：系统故障
    2：电源故障
    3：缺试剂
    4：缺蒸馏水
    5：加热故障
    6：排残液故障
    7：测量值超量程异常
    8：其他故障
    9：采集水样、试剂超时
    10：其他报警
*/
static const QMap<quint16, QString> DEVICE_FAULT_CODE_MAP = {
    {0, ErrorNames::COMMON_ERROR_NONE},
    {1, ErrorNames::NORMAL_ERROR_SYSTEM},
    {2, ErrorNames::NORMAL_ERROR_POWER},
    {3, ErrorNames::NORMAL_ERROR_REAGENT},
    {4, ErrorNames::NORMAL_ERROR_WATER},
    {5, ErrorNames::NORMAL_ERROR_HEATING},
    {6, ErrorNames::NORMAL_ERROR_WASTE_DISPOSAL},
    {7, ErrorNames::NORMAL_ERROR_OVER_RANGE},
    {8, ErrorNames::NORMAL_ERROR_OTHER},
    {9, ErrorNames::NORMAL_ERROR_REAGENT_INSUFFICIENT},
    {10, ErrorNames::NORMAL_ERROR_OTHER_ALARM},
};
DeviceHelper::DeviceHelper(QString deviceName, DeviceConfig deviceConfig, QObject* parent)
    : IDevice(deviceName, parent), m_deviceConfig(deviceConfig)
{
    m_deviceType = DeviceType_normal;
    m_operateMap = DEVICE_OPERATE_MAP;
    m_statusMap = DEVICE_STATUS_MAP;
    m_errorMap = DEVICE_FAULT_CODE_MAP;
    qDebug() << "DeviceHelper initialized with device name:" << m_deviceName;
}

DeviceHelper::~DeviceHelper()
{
    qDebug() << "DeviceWcsHelper destroyed for device name:" << m_deviceName;
}
void DeviceHelper::onGetDeviceData()
{
    if (m_busyFlags.dataBusy) {
        return;
    }
    m_busyFlags.dataBusy = true;
    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.dataBusy = false;
        return;
    }
    modbus->asyncReadFloat(1, 1)
        .then([this, modbus](const QVector<float>& result) {
            m_deviceData.analyze_lastResult = result[0];
            return modbus->asyncRead(18, 2);
        })
        .then([this, modbus](const QVector<quint16>& result) {
            m_deviceData.status = result[0];
            m_isIdle = m_deviceData.status == DEVICE_STATUS_IDLE;  // 更新空闲状态

            m_deviceData.faultCode = result[1];
            if (m_deviceData.faultCode != m_lastFaultCode && m_deviceData.faultCode != 0) {
                emit errorOccurred(QString("错误: %1").arg(getErrorName(m_deviceData.faultCode)));
            }
            m_lastFaultCode = m_deviceData.faultCode;

            return modbus->asyncRead(201, 6);
        })
        .then([this](const QVector<quint16>& result) {
            QDateTime datetime(QDate(result[0], result[1], result[2]),
                               QTime(result[3], result[4], result[5]));
            m_deviceData.datetime = datetime;
            emit deviceDataChanged(m_deviceData);
        })
        .finally([this]() { m_busyFlags.dataBusy = false; });
}
void DeviceHelper::setDeviceTime(const QDateTime& datetime)
{
    if (m_busyFlags.controlBusy) {
        return;
    }
    m_busyFlags.controlBusy = true;
    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.controlBusy = false;
        return;
    }
    QVector<quint16> data;
    data << 1 << datetime.date().year() << datetime.date().month() << datetime.date().day()
         << datetime.time().hour() << datetime.time().minute() << datetime.time().second();
    modbus->asyncWrite(200, data)
        .fail([this](const QString& error) {
            qDebug() << "Failed to write registers for device:" << m_deviceName
                     << "Error:" << error;
        })
        .finally([this]() { m_busyFlags.controlBusy = false; });
}
void DeviceHelper::control(quint16 addr, quint16 data)
{
    if (m_busyFlags.controlBusy) {
        return;
    }
    m_busyFlags.controlBusy = true;
    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.controlBusy = false;
        return;
    }
    QVector<quint16> _data;
    _data << data;
    modbus->asyncWrite(addr, _data)
        .fail([this](const QString& error) {
            qDebug() << "Failed to write register for device:" << m_deviceName << "Error:" << error;
        })
        .finally([this]() { m_busyFlags.controlBusy = false; });
}
void DeviceHelper::control(quint16 optCode) { control(optCode, 1); }
