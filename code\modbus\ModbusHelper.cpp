#include "ModbusHelper.h"

#include <QDebug>
using namespace QtPromise;
ModbusHelper::ModbusHelper(QObject* parent) : QObject(parent), modbus(new Modbus()) {}

ModbusHelper::~ModbusHelper() = default;

bool ModbusHelper::createModbus_rtu(const QString& portName, int baudRate, int slaveAddress)
{
#ifdef Q_OS_ANDROID
    int ret = system("echo 'chmod 777 /dev/ttyAS5' | su");
    if (ret != 0) {
        qDebug() << "chmod 777 /dev/ttyAS5 failed:" << ret;
    }
#endif
    bool success = modbus->set_config_rtu(portName.toStdString(), baudRate, slaveAddress);

    if (!success) {
        setError(QString::fromStdString(modbus->get_error_msg()));
    }
    return success;
}

bool ModbusHelper::createModbus_tcp(const QString& ipAddr, int port, int unit_id)
{
    bool success = modbus->set_config_tcp(ipAddr.toStdString(), port, unit_id);
    if (!success) {
        setError(QString::fromStdString(modbus->get_error_msg()));
    }
    return success;
}
void ModbusHelper::setError(const QString& error)
{
    lastError = error;
    emit errorOccurred();
}

bool ModbusHelper::writeRegister(int addr, quint16 value)
{
    return writeRegisters(addr, QVector<quint16>{value});
}

bool ModbusHelper::writeRegisters(int addr, const QVector<quint16>& values)
{
    auto data = toStdVector(values);
    bool success = modbus->write_data(addr, values.size(), data);
    if (!success) {
        setError(QString::fromStdString(modbus->get_error_msg()));
        return false;
    }
    emit commSuccess();
    return true;
}

bool ModbusHelper::writeRegisters(int addr, const QVector<float>& values)
{
    auto data = toStdVector(values);
    bool success = modbus->write_data(addr, values.size(), data);
    if (!success) {
        setError(QString::fromStdString(modbus->get_error_msg()));
        return false;
    }
    emit commSuccess();
    return true;
}
bool ModbusHelper::readRegisters(int addr, int count, QVector<quint16>& dest)
{
    std::vector<uint16_t> data(count);
    if (!modbus->read_data(addr, count, data)) {
        setError(QString::fromStdString(modbus->get_error_msg()));
        return false;
    }
    dest = toQVector(data);
    emit commSuccess();
    return true;
}
bool ModbusHelper::readRegisters(int addr, int count, float* dest)
{
    std::vector<float> data(count);
    if (!modbus->read_data(addr, count, data)) {
        setError(QString::fromStdString(modbus->get_error_msg()));
        return false;
    }
    for (int i = 0; i < count; i++) {
        dest[i] = data[i];
    }
    emit commSuccess();
    return true;
}

QPromise<QVector<quint16>> ModbusHelper::asyncRead(int addr, int count)
{
    return QPromise<QVector<quint16>>([=](const QPromiseResolve<QVector<quint16>>& resolve,
                                          const QPromiseReject<QVector<quint16>>& reject) {
        modbus->async_read_data(addr, count, [=](bool success, std::vector<uint16_t>& data) {
            if (success) {
                resolve(toQVector(data));
                emit commSuccess();
            } else {
                setError(QString::fromStdString(modbus->get_error_msg()));
                reject(lastError);
            }
        });
    });
}

QPromise<QVector<float>> ModbusHelper::asyncReadFloat(int addr, int count, MB_DataOrder order)
{
    return QPromise<QVector<float>>([=](const QPromiseResolve<QVector<float>>& resolve,
                                        const QPromiseReject<QVector<float>>& reject) {
        modbus->async_read_data(
            addr, count,
            [=](bool success, std::vector<float>& data) {
                if (success) {
                    resolve(toQVector(data));
                    emit commSuccess();
                } else {
                    setError(QString::fromStdString(modbus->get_error_msg()));
                    reject(lastError);
                }
            },
            order);
    });
}

QPromise<void> ModbusHelper::asyncWrite(int addr, const QVector<quint16>& values)
{
    return QPromise<void>(
        [=](const QPromiseResolve<void>& resolve, const QPromiseReject<void>& reject) {
            auto stdValues = toStdVector(values);
            modbus->async_write_data(addr, values.size(), stdValues, [=](bool success) {
                if (success) {
                    resolve();
                    emit commSuccess();
                } else {
                    setError(QString::fromStdString(modbus->get_error_msg()));
                    reject(lastError);
                }
            });
        });
}

QPromise<void> ModbusHelper::asyncWrite(int addr, const QVector<float>& values, MB_DataOrder order)
{
    return QPromise<void>(
        [=](const QPromiseResolve<void>& resolve, const QPromiseReject<void>& reject) {
            auto stdValues = toStdVector(values);
            modbus->async_write_data(
                addr, values.size(), stdValues,
                [=](bool success) {
                    if (success) {
                        resolve();
                        emit commSuccess();
                    } else {
                        setError(QString::fromStdString(modbus->get_error_msg()));
                        reject(lastError);
                    }
                },
                order);
        });
}