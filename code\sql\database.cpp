#include "database.h"

#include "common/ConstData.h"  // 包含 DEVICE_NAMES

void Database::initTable()
{
    QSqlQuery query;
    // 创建新表结构：时间戳、设备名称、测量值
    QString createTableSql =
        "CREATE TABLE IF NOT EXISTS device_readings ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT, "
        "timestamp TEXT NOT NULL, "       // 时间戳
        "device_name TEXT NOT NULL, "     // 设备名称
        "value REAL, "                    // 测量值
        "UNIQUE(timestamp, device_name)"  // 确保每个时间点每个设备只有一个读数
        ")";

    // 创建索引以加速查询
    QString createIndexSql =
        "CREATE INDEX IF NOT EXISTS idx_readings_timestamp "
        "ON device_readings (timestamp)";

    if (!query.exec(createTableSql) || !query.exec(createIndexSql)) {
        qWarning() << "数据库初始化失败：" << query.lastError().text() << "SQL:" << createTableSql;
    }
}

QVector<QVariantMap> Database::queryByDate(const QDate &date)
{
    QVector<QVariantMap> results;
    QSqlQuery query;

    // 准备设备名称的SQL部分
    QStringList deviceCases;
    for (const QString &name : DEVICE_NAMES) {
        if (DEVICE_MAP[name].isShowData) {
            QString escapedName = name;
            escapedName.replace("'", "''");  // 转义设备名中的单引号
            deviceCases << QString(
                               "MAX(CASE WHEN device_name = '%1' THEN value ELSE NULL END) AS '%1'")
                               .arg(escapedName);
        }
    }

    // SQL查询：按时间戳分组，将每个设备的值作为单独的列
    QString sql = QString(
                      "SELECT timestamp, %1 "
                      "FROM device_readings "
                      "WHERE 1=1")
                      .arg(deviceCases.join(", "));

    // 添加日期过滤条件
    if (date.isValid()) {
        QString dateStr = date.toString("yyyy-MM-dd");
        sql += " AND date(timestamp) = '" + dateStr + "'";
    }

    sql +=
        " GROUP BY timestamp "
        "ORDER BY timestamp DESC";  // 最新时间在前

    if (query.exec(sql)) {
        while (query.next()) {
            QVariantMap record;
            record["timestamp"] = query.value("timestamp");
            for (const QString &name : DEVICE_NAMES) {
                if (DEVICE_MAP[name].isShowData) {
                    record[name] = query.value(name);
                }
            }
            results.append(record);
        }
    } else {
        qWarning() << "查询失败：" << query.lastError().text() << "SQL:" << sql;
    }

    return results;
}

int Database::getTotalRecords(const QDate &date)
{
    QSqlQuery query;
    int count = 0;

    // 计算不同时间戳的总数（每个时间点算一条记录）
    QString sql = "SELECT COUNT(DISTINCT timestamp) FROM device_readings WHERE 1=1";
    if (date.isValid()) {
        QString dateStr = date.toString("yyyy-MM-dd");
        sql += " AND date(timestamp) = '" + dateStr + "'";
    }

    if (query.exec(sql) && query.next()) {
        count = query.value(0).toInt();
    } else {
        qWarning() << "获取总记录数失败：" << query.lastError().text() << "SQL:" << sql;
    }

    return count;
}

QVector<QVariantMap> Database::queryByPage(int page, int pageSize, const QDate &date)
{
    QVector<QVariantMap> results;
    QSqlQuery query;

    // 计算偏移量
    int offset = (page - 1) * pageSize;

    // 准备设备名称的SQL部分
    QStringList deviceCases;
    for (const QString &name : DEVICE_NAMES) {
        if (DEVICE_MAP[name].isShowData) {
            QString escapedName = name;
            escapedName.replace("'", "''");  // 转义设备名中的单引号
            deviceCases << QString(
                               "MAX(CASE WHEN device_name = '%1' THEN value ELSE NULL END) AS '%1'")
                               .arg(escapedName);
        }
    }

    // 分页查询：先获取要显示的时间戳
    QString timestampSql = QString("SELECT DISTINCT timestamp FROM device_readings WHERE 1=1");

    if (date.isValid()) {
        QString dateStr = date.toString("yyyy-MM-dd");
        timestampSql += " AND date(timestamp) = '" + dateStr + "'";
    }

    timestampSql += " ORDER BY timestamp DESC LIMIT " + QString::number(pageSize) + " OFFSET " +
                    QString::number(offset);

    // 主查询：根据已选时间戳获取数据
    QString sql = QString(
                      "SELECT timestamp, %1 "
                      "FROM device_readings "
                      "WHERE timestamp IN (SELECT timestamp FROM (%2)) "
                      "GROUP BY timestamp "
                      "ORDER BY timestamp DESC")
                      .arg(deviceCases.join(", "))
                      .arg(timestampSql);

    if (query.exec(sql)) {
        while (query.next()) {
            QVariantMap record;
            record["timestamp"] = query.value("timestamp");
            for (const QString &name : DEVICE_NAMES) {
                if (DEVICE_MAP[name].isShowData) {
                    record[name] = query.value(name);
                }
            }
            results.append(record);
        }
    } else {
        qWarning() << "分页查询失败：" << query.lastError().text() << "SQL:" << sql;
    }

    return results;
}

bool Database::insertHourlyData(const QDateTime &timestamp,
                                const QMap<QString, double> &deviceValues)
{
    // 自动将任意时间转换为整点时间（保留年月日小时，分钟秒设为0）
    QDateTime hourlyTimestamp = timestamp;
    hourlyTimestamp.setTime(QTime(timestamp.time().hour(), 0, 0));

    // 格式化时间戳为字符串
    QString timestampStr = hourlyTimestamp.toString("yyyy-MM-dd HH:00:00");

    QSqlQuery query;
    bool success = true;

    // 开始事务
    QSqlDatabase::database().transaction();

    try {
        // 准备插入语句
        query.prepare(
            "INSERT OR REPLACE INTO device_readings (timestamp, device_name, value) "
            "VALUES (?, ?, ?)");

        // 遍历设备数据
        QMapIterator<QString, double> i(deviceValues);
        while (i.hasNext()) {
            i.next();
            // 仅处理在DEVICE_MAP中定义且显示数据的设备
            if (DEVICE_MAP.contains(i.key()) && DEVICE_MAP[i.key()].isShowData) {
                query.bindValue(0, timestampStr);
                query.bindValue(1, i.key());
                query.bindValue(2, i.value());

                if (!query.exec()) {
                    qWarning() << "插入数据失败：" << query.lastError().text() << "设备:" << i.key()
                               << "值:" << i.value();
                    success = false;
                    break;
                }
            }
        }

        if (success) {
            QSqlDatabase::database().commit();
            return true;
        } else {
            QSqlDatabase::database().rollback();
            return false;
        }
    } catch (...) {
        QSqlDatabase::database().rollback();
        qWarning() << "插入数据异常，已回滚";
        return false;
    }
}