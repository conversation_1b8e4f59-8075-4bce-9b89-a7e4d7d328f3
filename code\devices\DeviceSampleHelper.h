#ifndef DEVICE_SAMPLE_HELPER_H
#define DEVICE_SAMPLE_HELPER_H

#include "IDevice.h"

class DeviceSampleHelper : public IDevice
{
    Q_OBJECT
public:
    DeviceSampleHelper(QString deviceName, QObject* parent = nullptr);
    ~DeviceSampleHelper();
    void control(quint16 optCode) override;
    void control(quint16 addr, quint16 data) override;

private slots:
    void onGetDeviceData() override;
};

#endif  // DEVICE_SAMPLE_HELPER_H