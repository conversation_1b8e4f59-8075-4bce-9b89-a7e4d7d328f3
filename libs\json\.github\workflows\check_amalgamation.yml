name: "Check amalgamation"

on:
  pull_request:

permissions:
  contents: read

jobs:
  save:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Save PR number
        run: |
          mkdir -p ./pr
          echo ${{ github.event.number }} > ./pr/number
          echo ${{ github.event.pull_request.user.login }} > ./pr/author
      - uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: pr
          path: pr/

  check:
    runs-on: ubuntu-latest
    env:
      MAIN_DIR: ${{ github.workspace }}/main
      INCLUDE_DIR: ${{ github.workspace }}/main/single_include/nlohmann
      TOOL_DIR: ${{ github.workspace }}/tools/tools/amalgamate

    steps:
      - name: Harden <PERSON>
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Checkout pull request
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: main
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Checkout tools
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: tools
          ref: develop

      - name: Install astyle
        run: |
          python3 -mvenv venv
          venv/bin/pip3 install -r $MAIN_DIR/tools/astyle/requirements.txt

      - name: Check amalgamation
        run: |
          cd $MAIN_DIR

          rm -fr $INCLUDE_DIR/json.hpp~ $INCLUDE_DIR/json_fwd.hpp~
          cp $INCLUDE_DIR/json.hpp $INCLUDE_DIR/json.hpp~
          cp $INCLUDE_DIR/json_fwd.hpp $INCLUDE_DIR/json_fwd.hpp~

          python3 $TOOL_DIR/amalgamate.py -c $TOOL_DIR/config_json.json -s .
          python3 $TOOL_DIR/amalgamate.py -c $TOOL_DIR/config_json_fwd.json -s .
          echo "Format (1)"
          ${{ github.workspace }}/venv/bin/astyle --project=tools/astyle/.astylerc --suffix=none --quiet $INCLUDE_DIR/json.hpp $INCLUDE_DIR/json_fwd.hpp

          diff $INCLUDE_DIR/json.hpp~ $INCLUDE_DIR/json.hpp
          diff $INCLUDE_DIR/json_fwd.hpp~ $INCLUDE_DIR/json_fwd.hpp

          ${{ github.workspace }}/venv/bin/astyle --project=tools/astyle/.astylerc --suffix=orig $(find docs/examples include tests -type f \( -name '*.hpp' -o -name '*.cpp' -o -name '*.cu' \) -not -path 'tests/thirdparty/*' -not -path 'tests/abi/include/nlohmann/*' | sort)
          echo Check
          find $MAIN_DIR -name '*.orig' -exec false {} \+
