#ifndef REALDATA_H
#define REALDATA_H

#include <QMap>
#include <QShowEvent>
#include <QTimer>
#include <QWidget>

#include "CardTemplate.h"
namespace Ui {
class realdata;
}

class realdata : public QWidget
{
    Q_OBJECT

public:
    explicit realdata(QWidget *parent = nullptr);
    ~realdata();

private:
    Ui::realdata *ui;
    QMap<QString, CardTemplate *> cardTemplates;
    QTimer *dataTimer;
};

#endif  // REALDATA_H
