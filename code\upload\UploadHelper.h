#ifndef UPLOADHELPER_H
#define UPLOADHELPER_H

#include <QObject>
#include <QThread>

#include "UploadConfig.h"

namespace Upload {

class UploadWorker;

class UploadHelper : public QObject
{
    Q_OBJECT
public:
    UploadHelper(const QString &id, const Config &config, QObject *parent = nullptr);
    ~UploadHelper();

    void start();
    void stop();
    bool isRunning() const;

signals:
    void uploadSuccess(const QString &id, const QString &data);
    void uploadError(const QString &id, const QString &error);
    void statusChanged(const QString &id, bool running);

    // 用于与worker通信
    void stopRequested();

private:
    QString m_id;
    QThread *m_thread;
    UploadWorker *m_worker;
    bool m_running;
};

}  // namespace Upload

#endif  // UPLOADHELPER_H
