#include "TaskStateMachine.h"

#include <QDebug>
#include <QTimer>

#include "ConstData.h"
#include "ConstNames.h"
#include "DeviceManager.h"

const int COMPOSITE_TASK_SPIKE_RECOVERY = 14;
const int COMPOSITE_TASK_ANALYSIS = 15;

// 使用全局常量定义操作名称映射
const QMap<AtomicOperation, QString> TaskStateMachine::OPERATION_NAMES = {
    {AtomicOperation::IDLE, OperationNames::SYSTEM_IDLE},
    {AtomicOperation::PREPROCESS, OperationNames::PRE_PROCESS},
    {AtomicOperation::ALGAE_REMOVAL, OperationNames::PRE_ALGAE_REMOVAL},
    {AtomicOperation::ZERO_CHECK, OperationNames::NORMAL_ZERO_CHECK},
    {AtomicOperation::STD_CHECK, OperationNames::NORMAL_STD_CHECK},
    {AtomicOperation::CALIB_FULL, OperationNames::NORMAL_CALIB_FULL},
    {AtomicOperation::SPIKE_INTAKE, OperationNames::SPIKE_INTAKE},
    {AtomicOperation::SPIKE_ANALYSIS, OperationNames::NORMAL_ANALYSIS},  // 加标测量用常规测量
    {AtomicOperation::SPIKE_DRAIN, OperationNames::SPIKE_DRAIN},
    {AtomicOperation::SAMPLE_INTAKE, OperationNames::SAMPLE_INTAKE},
    {AtomicOperation::ANALYSIS, OperationNames::NORMAL_ANALYSIS},
    {AtomicOperation::SAMPLE_DRAIN, OperationNames::SAMPLE_DRAIN},
    {AtomicOperation::CLEAN, OperationNames::NORMAL_CLEAN},
    {AtomicOperation::STOP_DEVICE, OperationNames::COMMON_STOP}};

TaskStateMachine::TaskStateMachine(QObject* parent)
    : QObject(parent),
      m_isRunning(false),
      m_currentTaskCode(0),
      m_currentStepIndex(-1),
      m_currentStepState(StepState::COMPLETED),
      m_stepTimer(nullptr),
      m_waitTimer(nullptr),
      m_monitorTimer(nullptr)
{
    // 创建定时器
    m_stepTimer = new QTimer(this);
    m_stepTimer->setSingleShot(true);
    connect(m_stepTimer, &QTimer::timeout, this, &TaskStateMachine::onStepTimeout);

    m_waitTimer = new QTimer(this);
    m_waitTimer->setSingleShot(true);
    connect(m_waitTimer, &QTimer::timeout, this, &TaskStateMachine::onWaitTimeout);

    m_monitorTimer = new QTimer(this);
    m_monitorTimer->setInterval(1000);  // 1秒检查一次
    connect(m_monitorTimer, &QTimer::timeout, this, &TaskStateMachine::checkStepCompletion);

    // 定义内置任务
    defineBuiltinTasks();
}

TaskStateMachine::~TaskStateMachine() { stop(); }

void TaskStateMachine::start()
{
    m_isRunning = true;
    qDebug() << "TaskStateMachine: 状态机已启动";
}

void TaskStateMachine::stop()
{
    if (m_isRunning) {
        // 清理当前任务状态
        cleanupCurrentTask();
        // 停止状态机
        m_isRunning = false;
        qDebug() << "TaskStateMachine: 状态机已停止";
    }
}

void TaskStateMachine::defineCompositeTask(int taskCode, const CompositeTask& task)
{
    m_compositeTasks[taskCode] = task;
    qDebug() << "TaskStateMachine: 定义复合任务" << taskCode << ":" << task.name;
}

bool TaskStateMachine::hasCompositeTask(int taskCode) const
{
    return m_compositeTasks.contains(taskCode);
}

CompositeTask TaskStateMachine::getCompositeTask(int taskCode) const
{
    return m_compositeTasks.value(taskCode, CompositeTask());
}

QList<int> TaskStateMachine::getAvailableTasks() const { return m_compositeTasks.keys(); }

bool TaskStateMachine::executeTask(int taskCode)
{
    if (!m_isRunning) {
        qDebug() << "TaskStateMachine: 状态机未启动";
        return false;
    }

    if (m_currentStepIndex >= 0) {
        qDebug() << "TaskStateMachine: 当前有任务正在执行";
        return false;
    }

    if (!m_compositeTasks.contains(taskCode)) {
        qDebug() << "TaskStateMachine: 未找到任务定义" << taskCode;
        return false;
    }

    // 清除之前的执行条件
    clearConditions();

    m_currentTask = m_compositeTasks[taskCode];
    m_currentTaskCode = taskCode;
    m_currentStepIndex = 0;

    qDebug() << "TaskStateMachine: 开始执行任务" << taskCode << ":" << m_currentTask.name;
    emit taskStarted(taskCode, m_currentTask.name);

    executeCurrentStep();
    return true;
}

bool TaskStateMachine::executeAtomicOperation(AtomicOperation operation, const QStringList& devices)
{
    if (!m_isRunning) {
        qDebug() << "TaskStateMachine: 状态机未启动";
        return false;
    }

    if (m_currentStepIndex >= 0) {
        qDebug() << "TaskStateMachine: 当前有任务正在执行";
        return false;
    }

    // 创建临时单步任务
    OperationStep step(operation, devices);
    step.description = getOperationName(operation);

    CompositeTask singleTask("单步操作");
    singleTask.steps.append(step);

    m_currentTask = singleTask;
    m_currentTaskCode = static_cast<int>(operation);
    m_currentStepIndex = 0;

    qDebug() << "TaskStateMachine: 执行原子操作" << getOperationName(operation);
    emit taskStarted(m_currentTaskCode, step.description);

    executeCurrentStep();
    return true;
}

void TaskStateMachine::cleanupCurrentTask()
{
    if (m_currentStepIndex >= 0) {
        m_stepTimer->stop();
        m_waitTimer->stop();
        m_monitorTimer->stop();
        m_currentTask = CompositeTask();
        m_currentTaskCode = 0;
        m_currentStepIndex = -1;
        m_currentStepState = StepState::COMPLETED;
        m_operatedDevices.clear();
        emit taskCompleted(m_currentTaskCode);
    }
}

void TaskStateMachine::stopAllDevices()
{
    auto deviceManager = DeviceManager::instance();
    deviceManager->stopAllDevices();
}

void TaskStateMachine::stopCurrentTask()
{
    qDebug() << "TaskStateMachine: 停止当前任务";
    stopAllDevices();
    cleanupCurrentTask();
}

QString TaskStateMachine::getCurrentTaskName() const
{
    return (m_currentStepIndex >= 0) ? m_currentTask.name : QString();
}

int TaskStateMachine::getCurrentStepIndex() const { return m_currentStepIndex; }

int TaskStateMachine::getTotalSteps() const
{
    return (m_currentStepIndex >= 0) ? m_currentTask.steps.size() : 0;
}

QString TaskStateMachine::getCurrentStepDescription() const
{
    if (m_currentStepIndex >= 0 && m_currentStepIndex < m_currentTask.steps.size()) {
        return m_currentTask.steps[m_currentStepIndex].description;
    }
    return QString();
}

AtomicOperation TaskStateMachine::getCurrentOperation() const
{
    if (m_currentStepIndex >= 0 && m_currentStepIndex < m_currentTask.steps.size()) {
        return m_currentTask.steps[m_currentStepIndex].operation;
    }
    return AtomicOperation::IDLE;
}

QString TaskStateMachine::getOperationName(AtomicOperation operation)
{
    return OPERATION_NAMES.value(operation, "未知操作");
}

void TaskStateMachine::setCondition(const QString& key, bool value)
{
    m_conditions[key] = value;
    qDebug() << "TaskStateMachine: 设置条件" << key << "=" << value;
}

bool TaskStateMachine::getCondition(const QString& key) const
{
    return m_conditions.value(key, true);  // 默认为true
}

void TaskStateMachine::clearConditions()
{
    m_conditions.clear();
    qDebug() << "TaskStateMachine: 清除所有条件";
}

void TaskStateMachine::onStepTimeout() { handleTimeout("步骤执行", "执行", "跳过执行超时的步骤"); }

void TaskStateMachine::onWaitTimeout()
{
    handleTimeout("等待设备操作完成", "等待", "跳过等待超时的步骤");
}

void TaskStateMachine::handleTimeout(const QString& timeoutType, const QString& failurePrefix,
                                     const QString& skipMessage)
{
    if (m_currentStepIndex >= 0 && m_currentStepIndex < m_currentTask.steps.size()) {
        const OperationStep& step = m_currentTask.steps[m_currentStepIndex];

        qDebug() << "TaskStateMachine:" << timeoutType << "超时";
        emit stepTimeout(m_currentStepIndex);

        if (step.abortOnError) {
            // 终止任务
            emit taskFailed(m_currentTaskCode,
                            QString("%1%2超时").arg(failurePrefix).arg(step.description));
            stopCurrentTask();
        } else {
            // 跳过此步骤，继续下一步
            qDebug() << "TaskStateMachine:" << skipMessage << m_currentStepIndex << "，继续执行";
            m_stepTimer->stop();
            m_waitTimer->stop();
            m_monitorTimer->stop();
            emit stepCompleted(m_currentStepIndex);
            executeNextStep();
        }
    }
}

void TaskStateMachine::checkStepCompletion()
{
    if (m_currentStepIndex < 0 || m_currentStepIndex >= m_currentTask.steps.size()) {
        m_monitorTimer->stop();
        return;
    }

    const OperationStep& step = m_currentTask.steps[m_currentStepIndex];

    if (m_currentStepState == StepState::PREPARING) {
        // 尝试执行操作
        QStringList actualOperatedDevices;
        QString errorMessage;
        bool executeSuccess =
            executeAtomicOperationInternal(step, actualOperatedDevices, errorMessage);

        if (executeSuccess) {
            // 执行成功，进入执行状态
            m_operatedDevices = actualOperatedDevices;
            m_currentStepState = StepState::EXECUTING;
            emit operationStarted(step.operation, actualOperatedDevices);
            qDebug() << "TaskStateMachine: 步骤" << m_currentStepIndex
                     << "操作启动成功，实际操作设备:" << actualOperatedDevices << "，进入执行状态";
        } else {
            // 执行失败（所有设备都无法执行），继续等待下次重试
            qDebug() << "TaskStateMachine: 步骤" << m_currentStepIndex
                     << "暂时无法执行:" << errorMessage;
        }
    } else if (m_currentStepState == StepState::EXECUTING) {
        // 检查已操作的设备是否变繁忙
        if (areAllDevicesBusy(m_operatedDevices)) {
            m_stepTimer->stop();
            qDebug() << "TaskStateMachine: 步骤" << m_currentStepIndex
                     << "已操作设备变繁忙，操作执行成功";
            emit operationCompleted(step.operation);

            // 检查是否需要等待条件
            if (step.waitTimeout > 0) {
                // 进入等待状态
                m_currentStepState = StepState::WAITING;
                qDebug() << "TaskStateMachine: 步骤" << m_currentStepIndex
                         << "开始等待设备操作完成";

                // 启动等待超时定时器
                m_waitTimer->start(step.waitTimeout * 1000);
            } else {
                // 无需等待，直接完成
                m_currentStepState = StepState::COMPLETED;
                emit stepCompleted(m_currentStepIndex);
                executeNextStep();
            }
        }
    } else if (m_currentStepState == StepState::WAITING) {
        // 检查等待设备操作完成是否满足
        QStringList completedDevices;
        bool conditionMet = checkDeviceCondition(completedDevices);
        if (conditionMet) {
            m_waitTimer->stop();
            m_currentStepState = StepState::COMPLETED;
            qDebug() << "TaskStateMachine: 步骤" << m_currentStepIndex
                     << "等待设备操作完成，已完成设备:" << completedDevices << "，步骤完成";
            emit stepCompleted(m_currentStepIndex);
            executeNextStep();
        }
    }
}

void TaskStateMachine::executeCurrentStep()
{
    if (m_currentStepIndex < 0 || m_currentStepIndex >= m_currentTask.steps.size()) {
        return;
    }

    const OperationStep& step = m_currentTask.steps[m_currentStepIndex];

    // 检查条件步骤是否应该执行
    if (!step.conditionKey.isEmpty()) {
        bool shouldExecute = getCondition(step.conditionKey);
        qDebug() << "TaskStateMachine: 条件步骤" << m_currentStepIndex << ":" << step.description
                 << "条件" << step.conditionKey << "=" << shouldExecute;

        if (!shouldExecute) {
            qDebug() << "TaskStateMachine: 跳过条件步骤" << m_currentStepIndex;
            emit stepStarted(m_currentStepIndex, step.description + " (跳过)");
            emit stepCompleted(m_currentStepIndex);
            executeNextStep();
            return;
        }
    }

    qDebug() << "TaskStateMachine: 执行步骤" << m_currentStepIndex << ":" << step.description;

    emit stepStarted(m_currentStepIndex, step.description);

    // 设置步骤状态为准备阶段
    m_currentStepState = StepState::PREPARING;
    m_operatedDevices.clear();

    // executeTimeout包含3个步骤：等待设备空闲→执行操作→判断设备繁忙
    m_stepTimer->start(step.executeTimeout * 1000);

    // 启动监控定时器（会定期尝试执行和检查状态）
    m_monitorTimer->start();
}

void TaskStateMachine::executeNextStep()
{
    m_currentStepIndex++;

    if (m_currentStepIndex >= m_currentTask.steps.size()) {
        // 任务完成
        qDebug() << "TaskStateMachine: 任务" << m_currentTask.name << "执行完成";
        emit taskCompleted(m_currentTaskCode);

        m_currentTask = CompositeTask();
        m_currentTaskCode = 0;
        m_currentStepIndex = -1;
    } else {
        // 继续执行下一步
        executeCurrentStep();
    }
}

bool TaskStateMachine::executeAtomicOperationInternal(const OperationStep& step,
                                                      QStringList& operatedDevices,
                                                      QString& errorMessage)
{
    QStringList targetDevices = step.targetDevices;
    if (targetDevices.isEmpty()) {
        targetDevices = getDeviceNames();
    }

    operatedDevices.clear();
    QString operationName = getOperationName(step.operation);
    QStringList busyDevices;
    QStringList errorDevices;

    // 逐个设备检查并执行操作
    for (const QString& deviceName : targetDevices) {
        auto helper = DeviceManager::instance()->getDeviceHelper(deviceName);
        if (!helper) {
            errorDevices.append(QString("%1(不存在)").arg(deviceName));
            continue;
        }
        if (!helper->isEnabled()) {
            errorDevices.append(QString("%1(未启用)").arg(deviceName));
            continue;
        }
        if (!helper->isIdle()) {
            busyDevices.append(deviceName);
            continue;
        }

        // 设备空闲，尝试执行操作
        qint16 operateCode = helper->getOperateMap().value(operationName, -1);
        if (operateCode != -1) {
            helper->control(operateCode);
            operatedDevices.append(deviceName);
            qDebug() << "TaskStateMachine: 设备" << deviceName << "执行操作" << operationName;
        } else {
            errorDevices.append(QString("%1(不支持操作%2)").arg(deviceName, operationName));
        }
    }

    // 构建错误消息
    QStringList errorMessages;
    if (!busyDevices.isEmpty()) {
        errorMessages.append(QString("繁忙设备: %1").arg(busyDevices.join(", ")));
    }
    if (!errorDevices.isEmpty()) {
        errorMessages.append(QString("错误设备: %1").arg(errorDevices.join(", ")));
    }

    if (!errorMessages.isEmpty()) {
        errorMessage = errorMessages.join("; ");
    }

    // 如果有设备成功执行操作，就认为部分成功
    if (!operatedDevices.isEmpty()) {
        if (!busyDevices.isEmpty() || !errorDevices.isEmpty()) {
            qDebug() << "TaskStateMachine: 部分设备执行成功，操作设备:" << operatedDevices
                     << "，问题:" << errorMessage;
        }
        return true;
    } else {
        // 没有任何设备成功执行
        return false;
    }
}

bool TaskStateMachine::areAllDevicesIdle(const QStringList& devices) const
{
    for (const QString& deviceName : devices) {
        auto helper = DeviceManager::instance()->getDeviceHelper(deviceName);
        if (helper && helper->isEnabled() && !helper->isIdle()) {
            return false;
        }
    }
    return true;
}

bool TaskStateMachine::areAllDevicesBusy(const QStringList& devices) const
{
    for (const QString& deviceName : devices) {
        auto helper = DeviceManager::instance()->getDeviceHelper(deviceName);
        if (!helper || !helper->isEnabled() || helper->isIdle()) {
            return false;
        }
    }
    return true;
}

bool TaskStateMachine::checkDeviceCondition(QStringList& completedDevices)
{
    // 遍历已操作的设备，检查状态
    QStringList errorDevices;
    QStringList busyDevices;
    QStringList localCompletedDevices;

    for (const QString& deviceName : m_operatedDevices) {
        auto helper = DeviceManager::instance()->getDeviceHelper(deviceName);
        if (!helper || !helper->isEnabled()) {
            continue;  // 跳过不存在或未启用的设备
        }

        // 1. 检查错误码
        if (helper->hasError()) {
            errorDevices.append(deviceName);
            continue;  // 设备有错误但不影响其他设备，继续检查下一个
        }

        // 2. 检查是否重新变为空闲
        if (!helper->isIdle()) {
            busyDevices.append(deviceName);
            continue;  // 设备还在忙碌，继续检查下一个
        }

        // 设备正常完成
        localCompletedDevices.append(deviceName);
    }
    // 如果有设备正常完成且没有设备在忙碌，则认为条件满足
    // 错误设备不影响整体判断
    if (!localCompletedDevices.isEmpty() && busyDevices.isEmpty()) {
        completedDevices = localCompletedDevices;
        qDebug() << "TaskStateMachine: 错误设备:" << errorDevices;
        qDebug() << "TaskStateMachine: 有设备正常完成且无忙碌设备，满足等待条件";
        return true;
    }

    // 还有设备在忙碌，继续等待
    return false;
}

QStringList TaskStateMachine::getDeviceNames() const
{
    QStringList normalDevices;
    for (const QString& deviceName : DEVICE_NAMES) {
        if (DEVICE_MAP.value(deviceName).deviceType != DeviceType_wcs) {
            normalDevices.append(deviceName);
        }
    }
    return normalDevices;
}

void TaskStateMachine::defineBuiltinTasks()
{
    defineAnalysisTask();
    defineSpikeRecoveryTask();

    qDebug() << "TaskStateMachine: 内置复合任务定义完成，共" << m_compositeTasks.size()
             << "个复合任务";
    qDebug() << "TaskStateMachine: 其他任务将作为单步操作执行";
}

void TaskStateMachine::defineAnalysisTask()
{
    // 水样测量任务：预处理 → 采样仪抽水 → 水样测量 → (条件)采样仪排水
    CompositeTask analysisTask("水样测量任务");
    analysisTask.description = "执行标准的水样测量流程，包含采样仪操作";

    // 步骤1：预处理（执行预处理 + 等待预处理完成）
    OperationStep preprocessStep(AtomicOperation::PREPROCESS);
    preprocessStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_ycl);
    preprocessStep.description = "预处理准备";
    preprocessStep.abortOnError = true;
    preprocessStep.waitTimeout = 60 * 60;  // 等待预处理完成超时：1小时
    analysisTask.steps.append(preprocessStep);

    // 步骤2：采样仪抽水（简单操作）
    OperationStep intakeStep(AtomicOperation::SAMPLE_INTAKE);
    intakeStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_sample);
    intakeStep.description = "采样仪抽水";
    analysisTask.steps.append(intakeStep);

    // 步骤3：水样测量（执行水样测量 + 等待水样测量完成）
    OperationStep analysisStep(AtomicOperation::ANALYSIS);
    analysisStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_normal);
    analysisStep.description = "水样测量";
    analysisStep.waitTimeout = 60 * 60;  // 等待测量完成超时：1小时
    analysisTask.steps.append(analysisStep);

    // 步骤4：采样仪排水（条件执行）
    OperationStep drainStep(AtomicOperation::SAMPLE_DRAIN);
    drainStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_sample);
    drainStep.description = "采样仪排水";
    drainStep.conditionKey = "need_drain";  // 需要排水的条件键
    analysisTask.steps.append(drainStep);

    defineCompositeTask(COMPOSITE_TASK_ANALYSIS, analysisTask);
}

void TaskStateMachine::defineSpikeRecoveryTask()
{
    // 加标回收任务：预处理 → 加标仪抽水 → 水样测量 → 加标测量 → 加标仪排水
    CompositeTask spikeTask("加标回收任务");
    spikeTask.description = "执行加标回收测试流程";

    // 步骤1：预处理（执行预处理 + 等待预处理完成）
    OperationStep preprocessStep(AtomicOperation::PREPROCESS);
    preprocessStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_ycl);
    preprocessStep.description = "预处理准备";
    preprocessStep.abortOnError = true;
    preprocessStep.waitTimeout = 60 * 60;  // 等待预处理完成超时：1小时
    spikeTask.steps.append(preprocessStep);

    // 步骤2：加标仪抽水（简单操作）
    OperationStep spikeIntakeStep(AtomicOperation::SPIKE_INTAKE);
    spikeIntakeStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_spike);
    spikeIntakeStep.description = "加标仪抽水";
    spikeTask.steps.append(spikeIntakeStep);

    // 步骤3：水样测量（执行水样测量 + 等待水样测量完成）
    OperationStep analysisStep(AtomicOperation::ANALYSIS);
    analysisStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_normal);
    analysisStep.description = "水样测量";
    analysisStep.waitTimeout = 60 * 60;  // 等待测量完成超时：1小时
    spikeTask.steps.append(analysisStep);

    // 步骤4：加标测量（执行加标测量 + 等待加标测量完成）
    OperationStep spikeAnalysisStep(AtomicOperation::SPIKE_ANALYSIS);
    spikeAnalysisStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_normal);
    spikeAnalysisStep.description = "加标测量";
    spikeAnalysisStep.waitTimeout = 60 * 60;  // 等待加标测量完成超时：1小时
    spikeTask.steps.append(spikeAnalysisStep);

    // 步骤5：加标仪排水（简单操作）
    OperationStep spikeDrainStep(AtomicOperation::SPIKE_DRAIN);
    spikeDrainStep.targetDevices = DeviceNames::getDeviceNamesByType(DeviceType_spike);
    spikeDrainStep.description = "加标仪排水";
    spikeTask.steps.append(spikeDrainStep);

    defineCompositeTask(COMPOSITE_TASK_SPIKE_RECOVERY, spikeTask);
}