#include "operate.h"

#include <QDebug>

#include "ConstData.h"
#include "operationWindow.h"
#include "ui_operate.h"
operate::operate(QWidget *parent) : QWidget(parent), ui(new Ui::operate)
{
    ui->setupUi(this);
    int i = 0;
    for (const QString &deviceId : DEVICE_NAMES) {
        if (DEVICE_MAP[deviceId].isOperable) {
            operationWindow *opWindow = new operationWindow(this, deviceId, DisplayMode::Embedded);
            ui->gridLayout->addWidget(opWindow, i / 3, i % 3);
            i++;
        }
    }
}

operate::~operate() { delete ui; }