#ifndef CONST_NAMES_H
#define CONST_NAMES_H

#include <QString>

// clang-format off
/**
 * @brief 操作名称常量定义
 * 集中管理所有操作的显示名称和内部标识符
 */
namespace OperationNames {
// 显示名称常量（用于UI显示）
    const QString SYSTEM_IDLE                        = "系统空闲";
    const QString COMMON_STOP                        = "停止设备";
    const QString COMMON_SET_TIME                    = "确定校时";

    // 预处理操作
    const QString PRE_PROCESS                        = "预处理";
    const QString PRE_ALGAE_REMOVAL                  = "除藻";
    const QString PRE_STEP_ALGAE_REMOVAL             = "单步除藻";
    const QString PRE_STEP_CLEAN                     = "单步清洗";
    const QString PRE_STEP_EMPTY_WATER               = "单步排空";
    const QString PRE_STEP_DRAW_WATER                = "单步抽水";
    const QString PRE_STEP_UPPER_WATER               = "单步配水";

    // 常规设备操作
    const QString NORMAL_ZERO_CHECK                  = "零点核查";
    const QString NORMAL_STD_CHECK                   = "标液核查";
    const QString NORMAL_CALIB_FULL                  = "校正标定";
    const QString NORMAL_CALIB_1                     = "校正标一";
    const QString NORMAL_CALIB_2                     = "校正标二";
    const QString NORMAL_ANALYSIS                    = "水样测量";
    const QString NORMAL_CLEAN                       = "仪表清洗";

    // 加标仪操作
    const QString SPIKE_INTAKE                       = "启动采样";
    const QString SPIKE_INTAKE_STOP                  = "停止采样";
    const QString SPIKE_DRAIN                        = "启动清洗";
    const QString SPIKE_DRAIN_STOP                   = "停止清洗";

    // 采样仪操作
    const QString SAMPLE_INTAKE                      = "启动采样";
    const QString SAMPLE_INTAKE_STOP                 = "停止采样";
    const QString SAMPLE_DRAIN                       = "启动排水";

    // 复合任务操作
    const QString TASK_ANALYSIS                      = "水样测量任务";
    const QString TASK_SPIKE_RECOVERY                = "加标回收任务";
}  // namespace OperationNames\

/**
 * @brief 错误名称常量定义
 * 集中管理所有错误的显示名称和内部标识符
 */
namespace ErrorNames {
    const QString COMMON_ERROR_NONE                  = "";
    // 预处理
    const QString PRE_ERROR_EMPTY_WATER              = "排空错误";
    const QString PRE_ERROR_CLEAN                    = "清洗错误";
    const QString PRE_ERROR_WATER_PUMP               = "抽水错误";
    const QString PRE_ERROR_WATER_MIX                = "配水错误";
    const QString PRE_ERROR_ALGAE_REMOVAL            = "除藻错误";
    const QString PRE_ERROR_OTHER                    = "其他错误";

    // 常规设备
    const QString NORMAL_ERROR_SYSTEM                = "系统故障";
    const QString NORMAL_ERROR_POWER                 = "电源故障";
    const QString NORMAL_ERROR_REAGENT               = "缺少试剂";
    const QString NORMAL_ERROR_WATER                 = "缺少蒸馏水";
    const QString NORMAL_ERROR_HEATING               = "加热故障";
    const QString NORMAL_ERROR_WASTE_DISPOSAL        = "排废故障";
    const QString NORMAL_ERROR_OVER_RANGE            = "测值超量程";
    const QString NORMAL_ERROR_OTHER                 = "其他错误";
    const QString NORMAL_ERROR_REAGENT_INSUFFICIENT  = "反应液不足";
    const QString NORMAL_ERROR_OTHER_ALARM           = "其他报警";

    // 加标仪
    const QString SPIKE_ERROR_FRIDGE_TEMP_LOW        = "冰箱温度过低";
    const QString SPIKE_ERROR_FRIDGE_TEMP_HIGH       = "冰箱温度过高";
    const QString SPIKE_ERROR_TEMPERATURE            = "温度故障";
    const QString SPIKE_ERROR_SCREEN_CONNECTION      = "屏连接失败";
    const QString SPIKE_ERROR_SPIKE_REAGENT_LOW      = "加标标液不足";
    const QString SPIKE_ERROR_MEASURE_REAGENT_LOW    = "测量标液不足";
    const QString SPIKE_ERROR_ZERO_LIQUID_LOW        = "零液不足";
    const QString SPIKE_ERROR_SPIKE_TANK_LOW         = "加标罐低液位故障";
    const QString SPIKE_ERROR_SPIKE_TANK_HIGH        = "加标罐高液位故障";

    // 采样仪
    const QString SAMPLE_ERROR_THERMOMETER           = "温度计故障";
    const QString SAMPLE_ERROR_FRIDGE_TEMP_OVER      = "冰箱温度超限";
    const QString SAMPLE_ERROR_DISTRIBUTOR_ARM       = "分配臂故障";
    const QString SAMPLE_ERROR_WATER_INSUFFICIENT    = "水量不足";
    const QString SAMPLE_ERROR_REAGENT_INSUFFICIENT  = "药品不足";
    const QString SAMPLE_ERROR_BOTTLE_FULL           = "瓶满";
    const QString SAMPLE_ERROR_FRIDGE_AUTO_PROTECT   = "冰箱自动保护";
    const QString SAMPLE_ERROR_LIFT_ROD              = "升降杆故障";
    const QString SAMPLE_ERROR_BOTTLE_DISTRIBUTE     = "分瓶故障";
}  // namespace ErrorNames

/**
 * @brief 设备状态名称常量定义
 * 集中管理所有设备的显示名称和内部标识符
 */
namespace StateNames {
    const QString STATE_IDLE                         = "空闲";

    // 预处理
    const QString PRE_STATE_IDLE                     = "空闲";
    const QString PRE_STATE_ALGAE_REMOVAL            = "除藻";
    const QString PRE_STATE_CLEAN                    = "清洗";
    const QString PRE_STATE_EMPTY_WATER              = "排空";
    const QString PRE_STATE_DRAW_WATER               = "抽水";
    const QString PRE_STATE_UPPER_WATER              = "配水";
    const QString PRE_STATE_SETTLE                   = "沉淀";

    // 常规设备
    const QString NORMAL_STATE_IDLE                  = "空闲";
    const QString NORMAL_STATE_RUNNING               = "运行";
    const QString NORMAL_STATE_MAINTENANCE           = "维护";
    const QString NORMAL_STATE_FAULT                 = "故障";
    const QString NORMAL_STATE_CALIB                 = "校正标定";
    const QString NORMAL_STATE_INSPECT               = "标液核查";
    const QString NORMAL_STATE_ANALYSIS              = "水样测量";
    const QString NORMAL_STATE_CALIBRATION           = "校正标定";
    const QString NORMAL_STATE_OPERATION             = "运维";
    const QString NORMAL_STATE_OTHER                 = "其他";

    // 加标仪
    const QString SPIKE_STATE_IDLE                   = "空闲";
    const QString SPIKE_STATE_RECOVERY               = "加标回收";
    const QString SPIKE_STATE_PARALLEL_SAMPLE        = "平行样";
    const QString SPIKE_STATE_STD_CHECK              = "标液核查";
    const QString SPIKE_STATE_ZERO_CHECK             = "零液核查";
    const QString SPIKE_STATE_CLEAN                  = "清洗";

    // 采样仪
    const QString SAMPLE_STATE_STANDBY               = "空闲";
    const QString SAMPLE_STATE_DRAIN_SAMPLE_LINE     = "留样管路排空";
    const QString SAMPLE_STATE_CLEAN_INTAKE          = "清洗-进水";
    const QString SAMPLE_STATE_CLEAN_DRAIN           = "清洗-排水";
    const QString SAMPLE_STATE_PRE_INTAKE_SAMPLE     = "预进水-提样";
    const QString SAMPLE_STATE_SAMPLING              = "采样";
    const QString SAMPLE_STATE_DRAIN_PIPELINE        = "采样管路排空";
    const QString SAMPLE_STATE_FINISHED              = "结束";

}  // namespace StateNames

#endif  // CONST_NAMES_H