<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserDialog</class>
 <widget class="QDialog" name="UserDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>426</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户信息</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>用户信息</string>
     </property>
     <layout class="QFormLayout" name="formLayout">
      <property name="horizontalSpacing">
       <number>20</number>
      </property>
      <property name="verticalSpacing">
       <number>15</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>30</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>20</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="label_username">
        <property name="text">
         <string>用户名:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEdit_username">
        <property name="placeholderText">
         <string>请输入用户名</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_password">
        <property name="text">
         <string>密码:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="lineEdit_password">
        <property name="echoMode">
         <enum>QLineEdit::Password</enum>
        </property>
        <property name="placeholderText">
         <string>请输入密码</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_confirm_password">
        <property name="text">
         <string>确认密码:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="lineEdit_confirm_password">
        <property name="echoMode">
         <enum>QLineEdit::Password</enum>
        </property>
        <property name="placeholderText">
         <string>请再次输入密码</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_role">
        <property name="text">
         <string>角色:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="comboBox_role">
        <item>
         <property name="text">
          <string>user</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>admin</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_status">
        <property name="text">
         <string>状态:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QComboBox" name="comboBox_status">
        <item>
         <property name="text">
          <string>启用</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>禁用</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
