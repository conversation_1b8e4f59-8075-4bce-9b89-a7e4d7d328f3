#ifndef ERRORLOG_H
#define ERRORLOG_H

#include <QDate>
#include <QDateTime>
#include <QDebug>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QString>
#include <QVariantMap>
#include <QVector>

class ErrorLog
{
public:
    static void initTable();

    // 插入错误记录
    static bool insertError(const QDateTime &timestamp, const QString &errorMessage);

    // 按日期查询错误记录
    static QVector<QVariantMap> queryByDate(const QDate &date);

    // 获取总记录数
    static int getTotalRecords(const QDate &date = QDate());

    // 分页查询错误记录
    static QVector<QVariantMap> queryByPage(int page, int pageSize, const QDate &date = QDate());

    // 清理过期错误记录（保留指定天数内的记录）
    static bool cleanOldRecords(int keepDays = 30);

private:
    ErrorLog() = delete;
    ~ErrorLog() = delete;
    ErrorLog(const ErrorLog &) = delete;
    ErrorLog &operator=(const ErrorLog &) = delete;
};

#endif  // ERRORLOG_H