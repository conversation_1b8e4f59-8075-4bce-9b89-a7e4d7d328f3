#include "SvgController.h"

#include <QApplication>
#include <QDebug>
#include <QPainter>
#include <QSize>
#include <QThread>

// ============== SvgRenderWorker 实现 ==============
void SvgRenderWorker::renderSvg(const QByteArray& svgData, const QSize& size)
{
    try {
        QSvgRenderer renderer;
        if (!renderer.load(svgData)) {
            emit renderFailed("SVG数据加载失败");
            return;
        }

        QSize renderSize = size.isValid() ? size : renderer.defaultSize();
        QPixmap pixmap(renderSize);
        pixmap.fill(Qt::transparent);

        QPainter painter(&pixmap);
        renderer.render(&painter);

        emit renderFinished(pixmap, renderSize);
    } catch (const std::exception& e) {
        emit renderFailed(QString("渲染异常: %1").arg(e.what()));
    } catch (...) {
        emit renderFailed("未知渲染错误");
    }
}

// ============== SvgController 实现 ==============
// 静态成员变量定义
SvgController* SvgController::m_instance = nullptr;

SvgController::SvgController(QObject* parent)
    : QObject(parent),
      m_svgRenderer(new QSvgRenderer(this)),
      m_renderThread(nullptr),
      m_renderWorker(nullptr)
{
    // 在构造函数中自动加载默认SVG文件
    loadDefaultSvgFile();
    initCustomComponents();

    // 初始化异步渲染组件
    initAsyncRendering();
}

SvgController::~SvgController()
{
    // 清理异步渲染组件
    cleanupAsyncRendering();
}

void SvgController::initAsyncRendering()
{
    // 创建渲染线程
    m_renderThread = new QThread(this);

    // 创建渲染工作对象
    m_renderWorker = new SvgRenderWorker();
    m_renderWorker->moveToThread(m_renderThread);

    // 连接信号
    connect(m_renderWorker, &SvgRenderWorker::renderFinished, this,
            &SvgController::onRenderFinished);
    connect(m_renderWorker, &SvgRenderWorker::renderFailed, this, &SvgController::onRenderFailed);

    // 启动线程
    m_renderThread->start();
}

void SvgController::cleanupAsyncRendering()
{
    if (m_renderThread) {
        m_renderThread->quit();
        m_renderThread->wait(3000);  // 等待3秒

        if (m_renderWorker) {
            m_renderWorker->deleteLater();
            m_renderWorker = nullptr;
        }

        m_renderThread = nullptr;
    }
}

SvgController* SvgController::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new SvgController();
    }
    return m_instance;
}

void SvgController::destroyInstance()
{
    if (m_instance != nullptr) {
        delete m_instance;
        m_instance = nullptr;
    }
}

bool SvgController::loadDefaultSvgFile()
{
    const QString defaultSvgPath = ":/icons/resource/icon/main.svg";
    m_svgFilePath = defaultSvgPath;

    QFile file(defaultSvgPath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开默认SVG文件：" << defaultSvgPath;
        return false;
    }

    m_originalSvgData = file.readAll();
    m_currentSvgData = m_originalSvgData;
    file.close();

    if (!m_svgRenderer->load(m_currentSvgData)) {
        qWarning() << "默认SVG文件加载失败：" << defaultSvgPath;
        return false;
    }

    qDebug() << "默认SVG文件加载成功：" << defaultSvgPath;
    return true;
}

void SvgController::initCustomComponents()
{
    // 添加设备数值标签
    for (int i = 1; i <= 4; i++) {
        QString textId = "LB" + QString::number(i);
        addTextAtPosition(textId, "---");
    }
}
QPixmap SvgController::getPixmap(const QSize& size) const
{
    if (!m_svgRenderer->isValid()) {
        qWarning() << "SVG渲染器无效";
        return QPixmap();
    }

    QSize renderSize = size.isValid() ? size : m_svgRenderer->defaultSize();
    QPixmap pixmap(renderSize);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    m_svgRenderer->render(&painter);

    return pixmap;
}

void SvgController::getPixmapAsync(const QSize& size, const QString& requestId)
{
    if (!m_renderWorker || !m_renderThread->isRunning()) {
        emit renderError("异步渲染器未就绪", requestId);
        return;
    }

    // 保存当前请求ID
    m_currentRequestId = requestId;

    // 线程安全地获取SVG数据
    QByteArray svgData;
    {
        QMutexLocker locker(&m_dataMutex);
        svgData = m_currentSvgData;
    }

    // 在工作线程中执行渲染
    QMetaObject::invokeMethod(m_renderWorker, "renderSvg", Qt::QueuedConnection,
                              Q_ARG(QByteArray, svgData), Q_ARG(QSize, size));
}

QFuture<QPixmap> SvgController::getPixmapConcurrent(const QSize& size)
{
    // 使用 QtConcurrent 进行异步渲染
    QByteArray svgData;
    {
        QMutexLocker locker(&m_dataMutex);
        svgData = m_currentSvgData;
    }

    return QtConcurrent::run(
        [svgData, size]() -> QPixmap { return SvgController::renderSvgSync(svgData, size); });
}

QPixmap SvgController::renderSvgSync(const QByteArray& svgData, const QSize& size)
{
    QSvgRenderer renderer;
    if (!renderer.load(svgData)) {
        qWarning() << "SVG数据加载失败";
        return QPixmap();
    }

    QSize renderSize = size.isValid() ? size : renderer.defaultSize();
    QPixmap pixmap(renderSize);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    renderer.render(&painter);

    return pixmap;
}

void SvgController::onRenderFinished(const QPixmap& pixmap, const QSize& requestedSize)
{
    emit pixmapReady(pixmap, requestedSize, m_currentRequestId);
}

void SvgController::onRenderFailed(const QString& error)
{
    emit renderError(error, m_currentRequestId);
}

void SvgController::setValveState(const QString& valveId, ValveState state)
{
    ValveState oldState = m_valveStates.value(valveId, VALVE_CLOSED);

    if (oldState == state) {
        return;  // 状态未改变
    }

    // 更新状态
    m_valveStates[valveId] = state;

    // 获取对应颜色
    QString color = getValveStateColor(state, valveId);

    // 更新SVG中的颜色
    if (updateValveColor(valveId, color)) {
        // 重新加载SVG
        m_svgRenderer->load(m_currentSvgData);

        // 发送信号
        emit valveStateChanged(valveId, state);
        emit svgUpdated();

        // qDebug() << "阀门" << valveId << "状态更新为：" << (state == VALVE_OPEN ? "开启" :
        // "关闭")
        //          << "颜色：" << color;
    } else {
        qWarning() << "阀门" << valveId << "颜色更新失败";
        // 回滚状态
        m_valveStates[valveId] = oldState;
    }
}

SvgController::ValveState SvgController::getValveState(const QString& valveId) const
{
    return m_valveStates.value(valveId, VALVE_CLOSED);
}

void SvgController::resetAllValves()
{
    QStringList valveIds = m_valveStates.keys();

    for (const QString& valveId : valveIds) {
        setValveState(valveId, VALVE_CLOSED);
    }

    qDebug() << "所有阀门已重置为关闭状态";
}

bool SvgController::updateValveColor(const QString& valveId, const QString& color)
{
    if (m_originalSvgData.isEmpty()) {
        qWarning() << "原始SVG数据为空";
        return false;
    }

    QString svgString = QString::fromUtf8(m_currentSvgData);

    // 更通用的正则表达式，处理各种情况
    // 匹配包含指定ID的整个标签，然后在其中查找并替换fill属性
    QString tagPattern = QString("<([^>]*id=\"%1\"[^>]*)>").arg(valveId);
    QRegularExpression tagRegex(tagPattern);

    QRegularExpressionMatch match = tagRegex.match(svgString);
    if (match.hasMatch()) {
        QString fullTag = match.captured(0);     // 完整的标签
        QString tagContent = match.captured(1);  // 标签内容

        // 在标签内容中查找fill属性
        QRegularExpression fillRegex("fill=\"[^\"]*\"");
        if (fillRegex.match(tagContent).hasMatch()) {
            // 替换fill属性值
            QString newTagContent = tagContent;
            newTagContent.replace(fillRegex, QString("fill=\"%1\"").arg(color));
            QString newFullTag = QString("<%1>").arg(newTagContent);

            // 在原始字符串中替换
            svgString.replace(fullTag, newFullTag);

            // qDebug() << "成功更新阀门" << valveId << "颜色为" << color;

            m_currentSvgData = svgString.toUtf8();
            return true;
        } else {
            qWarning() << "标签中未找到fill属性：" << fullTag;
            return false;
        }
    }

    // 如果上面的方法失败，尝试处理自闭合标签的情况
    QString selfClosingPattern = QString("<([^>]*id=\"%1\"[^>]*/)>").arg(valveId);
    QRegularExpression selfClosingRegex(selfClosingPattern);

    match = selfClosingRegex.match(svgString);
    if (match.hasMatch()) {
        QString fullTag = match.captured(0);     // 完整的自闭合标签
        QString tagContent = match.captured(1);  // 标签内容（不包括最后的/）

        qDebug() << "找到自闭合标签：" << fullTag;

        // 在标签内容中查找fill属性
        QRegularExpression fillRegex("fill=\"[^\"]*\"");
        if (fillRegex.match(tagContent).hasMatch()) {
            // 替换fill属性值
            QString newTagContent = tagContent;
            newTagContent.replace(fillRegex, QString("fill=\"%1\"").arg(color));
            QString newFullTag = QString("<%1>").arg(newTagContent);

            // 在原始字符串中替换
            svgString.replace(fullTag, newFullTag);

            qDebug() << "成功更新自闭合阀门" << valveId << "颜色为" << color;
            qDebug() << "新标签：" << newFullTag;

            m_currentSvgData = svgString.toUtf8();
            return true;
        } else {
            qWarning() << "自闭合标签中未找到fill属性：" << fullTag;
            return false;
        }
    }

    qWarning() << "未找到阀门ID：" << valveId;
    qDebug() << "尝试搜索包含该ID的所有元素...";

    // 调试：搜索所有包含该ID的行
    QStringList lines = svgString.split('\n');
    for (int i = 0; i < lines.size(); ++i) {
        if (lines[i].contains(QString("id=\"%1\"").arg(valveId))) {
            qDebug() << "找到ID" << valveId << "在第" << (i + 1) << "行：" << lines[i].trimmed();
        }
    }
    return false;
}

QString SvgController::getValveStateColor(ValveState state, const QString& componentId) const
{
    switch (state) {
        case VALVE_OPEN:
            return "#00FF00";  // 绿色 - 开启
        case VALVE_CLOSED:
        default:
            // 根据组件ID前缀判断关闭颜色
            if (componentId.startsWith("SV")) {
                return "#5890BE";  // 蓝色 - SV系列阀门关闭
            } else if (componentId.startsWith("M")) {
                return "#9FAAB3";  // 灰色 - M系列电机关闭
            }
            return "#5890BE";  // 默认蓝色
    }
}

bool SvgController::addTextToSvg(const QString& textId, const QString& text,
                                 const QPointF& position, int fontSize, const QString& color,
                                 const QString& fontFamily)
{
    if (m_currentSvgData.isEmpty()) {
        qWarning() << "SVG数据为空";
        return false;
    }

    QString svgString = QString::fromUtf8(m_currentSvgData);

    // 先检查文本元素是否已存在
    QString existingPattern = QString("<text[^>]*id=\"%1\"").arg(textId);
    QRegularExpression existingRegex(existingPattern);
    if (existingRegex.match(svgString).hasMatch()) {
        qWarning() << "文本元素" << textId << "已存在";
        return false;
    }

    // 创建文本元素
    QString textElement = QString(
                              "<text id=\"%1\" x=\"%2\" y=\"%3\" font-size=\"%4\" fill=\"%5\" "
                              "font-family=\"%6\" font-weight=\"900\" text-anchor=\"middle\" "
                              "dominant-baseline=\"middle\">%7</text>\n")
                              .arg(textId)
                              .arg(position.x())
                              .arg(position.y())
                              .arg(fontSize)
                              .arg(color)
                              .arg(fontFamily)
                              .arg(text);

    // 在</svg>标签前插入文本元素
    int insertPos = svgString.lastIndexOf("</svg>");
    if (insertPos == -1) {
        qWarning() << "未找到</svg>结束标签";
        return false;
    }

    svgString.insert(insertPos, textElement);

    // 更新SVG数据并重新渲染
    m_currentSvgData = svgString.toUtf8();
    if (m_svgRenderer->load(m_currentSvgData)) {
        emit svgUpdated();
        // qDebug() << "成功添加文本元素：" << textId << "内容：" << text << "位置：" << position;
        return true;
    } else {
        qWarning() << "SVG重新加载失败";
        return false;
    }
}

bool SvgController::updateTextInSvg(const QString& textId, const QString& newText)
{
    if (m_currentSvgData.isEmpty()) {
        qWarning() << "SVG数据为空";
        return false;
    }

    QString svgString = QString::fromUtf8(m_currentSvgData);

    // 查找文本元素
    QString pattern = QString("<text[^>]*id=\"%1\"[^>]*>([^<]*)</text>").arg(textId);
    QRegularExpression regex(pattern);
    QRegularExpressionMatch match = regex.match(svgString);

    if (match.hasMatch()) {
        QString fullMatch = match.captured(0);
        QString oldText = match.captured(1);

        // 替换文本内容
        QString newFullMatch = fullMatch;
        newFullMatch.replace(oldText, newText);
        svgString.replace(fullMatch, newFullMatch);

        // 更新SVG数据并重新渲染
        m_currentSvgData = svgString.toUtf8();
        if (m_svgRenderer->load(m_currentSvgData)) {
            emit svgUpdated();
            return true;
        } else {
            qWarning() << "SVG重新加载失败";
            return false;
        }
    } else {
        qWarning() << "未找到文本元素：" << textId;
        return false;
    }
}

bool SvgController::addTextAtPosition(const QString& positionId, const QString& text,
                                      const QPointF& offset)
{
    // 获取位置元素的中心坐标
    QPointF center = getValveCenter(positionId);
    if (center == QPointF(-1, -1)) {
        qWarning() << "未找到位置元素：" << positionId;
        return false;
    }

    // 计算文本位置，稍微向上偏移以获得更好的视觉居中效果
    QPointF textPosition = center + offset + QPointF(0, 8);

    // 生成文本ID
    QString textId = positionId;

    // 使用更适合数字显示的字体样式
    return addTextToSvg(textId, text, textPosition, 24, "#4fc3f7", "Consolas, monospace");
}

bool SvgController::removeTextFromSvg(const QString& textId)
{
    if (m_currentSvgData.isEmpty()) {
        qWarning() << "SVG数据为空";
        return false;
    }

    QString svgString = QString::fromUtf8(m_currentSvgData);

    // 查找并移除文本元素
    QString pattern = QString("<text[^>]*id=\"%1\"[^>]*>.*?</text>\\s*").arg(textId);
    QRegularExpression regex(pattern);

    if (regex.match(svgString).hasMatch()) {
        svgString.remove(regex);

        // 更新SVG数据并重新渲染
        m_currentSvgData = svgString.toUtf8();
        if (m_svgRenderer->load(m_currentSvgData)) {
            emit svgUpdated();
            qDebug() << "成功移除文本元素：" << textId;
            return true;
        } else {
            qWarning() << "SVG重新加载失败";
            return false;
        }
    } else {
        qWarning() << "未找到文本元素：" << textId;
        return false;
    }
}

QRectF SvgController::getValveBoundingRect(const QString& valveId) const
{
    if (!m_svgRenderer->isValid()) {
        qWarning() << "SVG渲染器无效";
        return QRectF();
    }

    // 使用QSvgRenderer获取指定元素的边界框
    QRectF boundingRect = m_svgRenderer->boundsOnElement(valveId);

    if (boundingRect.isValid()) {
        // qDebug() << "阀门" << valveId << "边界框："
        //          << "x=" << boundingRect.x() << "y=" << boundingRect.y()
        //          << "width=" << boundingRect.width() << "height=" << boundingRect.height();
        return boundingRect;
    } else {
        qWarning() << "未找到阀门" << valveId << "的边界框";
        return QRectF();
    }
}

QPointF SvgController::getValveCenter(const QString& valveId) const
{
    QRectF boundingRect = getValveBoundingRect(valveId);

    if (boundingRect.isValid()) {
        QPointF center = boundingRect.center();
        // qDebug() << "阀门" << valveId << "中心坐标："
        //          << "x=" << center.x() << "y=" << center.y();
        return center;
    } else {
        qWarning() << "无法获取阀门" << valveId << "的中心坐标";
        return QPointF(-1, -1);
    }
}

bool SvgController::setComponentColor(const QString& componentId, const QString& color)
{
    if (m_currentSvgData.isEmpty()) {
        qWarning() << "SVG数据为空";
        return false;
    }

    // 使用现有的updateValveColor方法，但是是公开调用
    if (updateValveColor(componentId, color)) {
        // 重新加载SVG
        if (m_svgRenderer->load(m_currentSvgData)) {
            emit svgUpdated();
            return true;
        } else {
            qWarning() << "SVG重新加载失败";
            return false;
        }
    } else {
        qWarning() << "组件" << componentId << "颜色设置失败";
        return false;
    }
}