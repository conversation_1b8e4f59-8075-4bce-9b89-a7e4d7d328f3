#ifndef SYSTEM_H
#define SYSTEM_H

#include <QObject>

#include "SystemConfig.h"
#include "SystemScheduler.h"

/**
 * @brief 系统协调器类 (重构后的System)
 * 负责协调配置管理器和调度器，提供统一的对外接口
 */
class System : public QObject
{
    Q_OBJECT

public:
    static System* instance();
    ~System();

    // 系统控制
    void start();
    void stop();
    bool isRunning() const;

    // 配置管理（委托给SystemConfigManager）
    SystemConfig getConfig() const;
    bool setConfig(const SystemConfig& config);
    bool checkConfigTimeConflict(const SystemConfig& config);
    bool isScheduled(int hour) const;

    // 信息获取（委托给SystemScheduler）
    SystemInfo getInfo() const;
    QString getTaskName(qint16 operateCode) const;
    // 任务执行
    void execTask(qint16 operateCode);

signals:
    void infoChanged(const SystemInfo& info);
    void isRunningChanged(bool isRunning);
    void configChanged(const SystemConfig& config);
    void currentTaskChanged(qint16 operateCode);
    void taskFailed(qint16 operateCode, const QString& reason);  // 任务失败信号

private:
    explicit System(QObject* parent = nullptr);
    void initConnections();

    SystemConfigManager* m_configManager;
    SystemScheduler* m_scheduler;
};

#endif  // SYSTEM_V2_H