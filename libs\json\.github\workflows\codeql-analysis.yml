name: "Code scanning - action"

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  schedule:
    - cron: '0 19 * * 1'
  workflow_dispatch:
  
concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  CodeQL-Build:

    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
      with:
        egress-policy: audit

    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@b6a472f63d85b9c78a3ac5e89422239fc15e9b3c # v3.28.1
      with:
        languages: c-cpp

    # Autobuild attempts to build any compiled languages  (C/C++, C#, or Java).
    # If this step fails, then you should remove it and run the build manually (see below)
    - name: Autobuild
      uses: github/codeql-action/autobuild@b6a472f63d85b9c78a3ac5e89422239fc15e9b3c # v3.28.1

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@b6a472f63d85b9c78a3ac5e89422239fc15e9b3c # v3.28.1
