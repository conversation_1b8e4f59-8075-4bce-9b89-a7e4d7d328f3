#ifndef OPERATIONWINDOW_H
#define OPERATIONWINDOW_H

#include <QFrame>
#include <QHideEvent>
#include <QShowEvent>

#include "DeviceManager.h"
namespace Ui {
class operationWindow;
}

enum class DisplayMode {
    Popup,    // 弹窗模式
    Embedded  // 嵌入模式
};

class operationWindow : public QFrame
{
    Q_OBJECT

public:
    explicit operationWindow(QWidget *parent = nullptr, QString deviceId = "",
                             DisplayMode mode = DisplayMode::Embedded, bool isTimeSync = false);
    ~operationWindow();
private slots:
    void onCommStatusChanged(const bool &success);
    void onDeviceDataChanged(const DeviceData &data);

private:
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void handleParameterButtonClicked(int buttonId);
    Ui::operationWindow *ui;
    DisplayMode m_displayMode;
    QString m_deviceId;
};

#endif  // OPERATIONWINDOW_H
