#include "common.h"

unsigned int Common::CRC16_Checkout(unsigned char *puchMsg, unsigned int usDataLen)
{
    unsigned int i, j, crc_reg, check;
    crc_reg = 0xFFFF;
    for (i = 0; i < usDataLen; i++) {
        crc_reg = (crc_reg >> 8) ^ puchMsg[i];
        for (j = 0; j < 8; j++) {
            check = crc_reg & 0x0001;
            crc_reg >>= 1;
            if (check == 0x0001) {
                crc_reg ^= 0xA001;
            }
        }
    }
    return crc_reg;
}
