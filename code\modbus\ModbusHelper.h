#ifndef MODBUS_HELPER_H
#define MODBUS_HELPER_H

#include <QMutex>
#include <QObject>
#include <QtPromise>

#include "modbus.hpp"

class ModbusHelper : public QObject
{
    Q_OBJECT

public:
    explicit ModbusHelper(QObject* parent = nullptr);
    ~ModbusHelper();

    // 基础连接功能
    bool createModbus_tcp(const QString& ipAddr, int port, int unit_id = 1);
    bool createModbus_rtu(const QString& portName, int baudRate = 38400, int slaveAddress = 1);
    QString getLastError() const { return lastError; };

    // 同步读写接口
    bool writeRegister(int addr, quint16 value);
    bool writeRegisters(int addr, const QVector<quint16>& values);
    bool writeRegisters(int addr, const QVector<float>& values);
    bool readRegisters(int addr, int count, QVector<quint16>& dest);
    bool readRegisters(int addr, int count, float* dest);

    // Promise 风格的异步读写接口
    QtPromise::QPromise<QVector<quint16>> asyncRead(int addr, int count);
    QtPromise::QPromise<QVector<float>> asyncReadFloat(int addr, int count,
                                                       MB_DataOrder order = MB_DataOrder::MB_ABCD);
    QtPromise::QPromise<void> asyncWrite(int addr, const QVector<quint16>& values);
    QtPromise::QPromise<void> asyncWrite(int addr, const QVector<float>& values,
                                         MB_DataOrder order = MB_DataOrder::MB_ABCD);

signals:
    void errorOccurred();
    void commSuccess();

private:
    std::unique_ptr<Modbus> modbus;
    QString lastError;

    void setError(const QString& error);

    // 辅助函数：std::vector 和 QVector 转换
    template <typename T>
    static QVector<T> toQVector(const std::vector<T>& vec)
    {
        return QVector<T>(vec.begin(), vec.end());
    }

    template <typename T>
    static std::vector<T> toStdVector(const QVector<T>& vec)
    {
        return std::vector<T>(vec.begin(), vec.end());
    }
};

#endif  // MODBUS_HELPER_H