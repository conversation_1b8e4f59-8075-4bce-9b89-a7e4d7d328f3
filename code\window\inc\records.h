#ifndef RECORDS_H
#define RECORDS_H

#include <QButtonGroup>
#include <QDebug>
#include <QResizeEvent>

#include "../../sql/database.h"
#include "RecordDataHelper.h"
namespace Ui {
class records;
}

class records : public QWidget
{
    Q_OBJECT

public:
    explicit records(QWidget *parent = nullptr);
    ~records();

private slots:
    void on_bt_prePage_clicked();
    void on_bt_nextPage_clicked();
    void on_bt_headPage_clicked();
    void on_bt_tailPage_clicked();
    void on_bt_export_clicked();
    void on_bt_exec_clicked();
    void on_bt_purge_clicked();
    void onMenuButtonClicked(int id);

protected:
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    Ui::records *ui;
    QButtonGroup *menuButtonGroup;  // 菜单按钮组

    // 当前记录类型
    RecordType currentRecordType = RecordType::DeviceData;  // 默认显示设备数据

    // 分页相关变量
    int currentPage = 1;   // 当前页码
    int pageSize = 10;     // 每页显示记录数
    int totalPages = 1;    // 总页数
    int totalRecords = 0;  // 总记录数

    // 日期查询条件
    QDate queryDate;  // 查询日期，默认为无效日期（查询所有）

    // 加载数据的辅助函数
    void loadPageData();
    void updatePageInfo();
    void updatePageSize();
    void setRowHeightByRecordType(RecordType type);
};

#endif  // RECORDS_H
