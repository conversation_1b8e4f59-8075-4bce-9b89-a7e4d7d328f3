#include "DeviceSpikeHelper.h"

#include <QDebug>

#include "ConstNames.h"
#include "ModbusManager.h"
#define REG_SYSTEM_STATE 0x04      // 系统状态
#define REG_SYSTEM_ERROR 0x0D      // 系统错误
#define REG_SAMPLING_CONTROL 0x99  // 采样控制寄存器
#define REG_CLEAN_CONTROL 0x9A     // 清洗控制寄存器
#define REG_TIME_YEAR_MONTH 0x6B   // 时钟：年(高8位)+月(低8位)
#define REG_TIME_DAY_HOUR 0x6C     // 时钟：日(高8位)+时(低8位)
#define REG_TIME_MIN_SEC 0x6D      // 时钟：分(高8位)+秒(低8位)

// 控制命令值
#define CONTROL_START 1  // 启动
#define CONTROL_STOP 0   // 停止

// 加标设备状态定义
typedef enum {
    STATE_IDLE = 0,         // 空闲
    STATE_RECOVERY,         // 加标回收
    STATE_PARALLEL_SAMPLE,  // 平行样
    STATE_STD_CHECK,        // 标液核查
    STATE_ZERO_CHECK,       // 零液核查
    STATE_CLEAN,            // 清洗
} spike_state_t;

typedef enum {
    CMD_START = 1,       // 启动采样
    CMD_CLEAN = 2,       // 清洗
    CMD_STOP_ALL = 3,    // 停止采样
    CMD_STOP_CLEAN = 4,  // 停止清洗
    CMD_SET_TIME = 5,    // 确定校时
} spike_cmd_t;

// 操作到寄存器地址的映射
static const QMap<quint16, quint16> SPIKE_CMD_ADDR_MAP = {
    {CMD_START, REG_SAMPLING_CONTROL},
    {CMD_STOP_ALL, REG_SAMPLING_CONTROL},
    {CMD_CLEAN, REG_CLEAN_CONTROL},
    {CMD_STOP_CLEAN, REG_CLEAN_CONTROL},
};

// 操作到控制值的映射
static const QMap<quint16, quint16> SPIKE_CMD_VALUE_MAP = {
    {CMD_START, CONTROL_START},
    {CMD_STOP_ALL, CONTROL_STOP},
    {CMD_CLEAN, CONTROL_START},
    {CMD_STOP_CLEAN, CONTROL_STOP},
};

static const QMap<QString, quint16> SPIKE_OPERATE_MAP = {
    {OperationNames::SPIKE_INTAKE, CMD_START},
    {OperationNames::SPIKE_INTAKE_STOP, CMD_STOP_ALL},
    {OperationNames::SPIKE_DRAIN, CMD_CLEAN},
    {OperationNames::SPIKE_DRAIN_STOP, CMD_STOP_CLEAN},
    {OperationNames::COMMON_SET_TIME, CMD_SET_TIME},
};

static const QMap<quint16, QString> SPIKE_STATE_MAP = {
    {STATE_IDLE, StateNames::SPIKE_STATE_IDLE},
    {STATE_RECOVERY, StateNames::SPIKE_STATE_RECOVERY},
    {STATE_PARALLEL_SAMPLE, StateNames::SPIKE_STATE_PARALLEL_SAMPLE},
    {STATE_ZERO_CHECK, StateNames::SPIKE_STATE_ZERO_CHECK},
    {STATE_STD_CHECK, StateNames::SPIKE_STATE_STD_CHECK},
    {STATE_CLEAN, StateNames::SPIKE_STATE_CLEAN},
};

typedef enum {
    ERROR_NONE = 0,             // 无错误
    ERROR_FRIDGE_TEMP_LOW,      // 冰箱温度过低
    ERROR_FRIDGE_TEMP_HIGH,     // 冰箱温度过高
    ERROR_TEMPERATURE,          // 温度故障
    ERROR_SCREEN_CONNECTION,    // 屏连接失败
    ERROR_SPIKE_REAGENT_LOW,    // 加标标液不足
    ERROR_MEASURE_REAGENT_LOW,  // 测量标液不足
    ERROR_ZERO_LIQUID_LOW,      // 零液不足
    ERROR_SPIKE_TANK_LOW,       // 加标罐低液位故障
    ERROR_SPIKE_TANK_HIGH,      // 加标罐高液位故障
} spike_error_type_t;

static const QMap<quint16, QString> SPIKE_ERROR_TYPE_MAP = {
    {ERROR_NONE, ErrorNames::COMMON_ERROR_NONE},
    {ERROR_FRIDGE_TEMP_LOW, ErrorNames::SPIKE_ERROR_FRIDGE_TEMP_LOW},
    {ERROR_FRIDGE_TEMP_HIGH, ErrorNames::SPIKE_ERROR_FRIDGE_TEMP_HIGH},
    {ERROR_TEMPERATURE, ErrorNames::SPIKE_ERROR_TEMPERATURE},
    {ERROR_SCREEN_CONNECTION, ErrorNames::SPIKE_ERROR_SCREEN_CONNECTION},
    {ERROR_SPIKE_REAGENT_LOW, ErrorNames::SPIKE_ERROR_SPIKE_REAGENT_LOW},
    {ERROR_MEASURE_REAGENT_LOW, ErrorNames::SPIKE_ERROR_MEASURE_REAGENT_LOW},
    {ERROR_ZERO_LIQUID_LOW, ErrorNames::SPIKE_ERROR_ZERO_LIQUID_LOW},
    {ERROR_SPIKE_TANK_LOW, ErrorNames::SPIKE_ERROR_SPIKE_TANK_LOW},
    {ERROR_SPIKE_TANK_HIGH, ErrorNames::SPIKE_ERROR_SPIKE_TANK_HIGH},
};

DeviceSpikeHelper::DeviceSpikeHelper(QString deviceName, QObject* parent)
    : IDevice(deviceName, parent)
{
    m_deviceType = DeviceType_spike;
    m_operateMap = SPIKE_OPERATE_MAP;
    m_statusMap = SPIKE_STATE_MAP;
    m_errorMap = SPIKE_ERROR_TYPE_MAP;
    qDebug() << "DeviceSpikeHelper initialized with device name:" << m_deviceName;
}

DeviceSpikeHelper::~DeviceSpikeHelper()
{
    qDebug() << "DeviceSpikeHelper destroyed for device name:" << m_deviceName;
}

void DeviceSpikeHelper::control(quint16 addr, quint16 data)
{
    if (m_busyFlags.controlBusy) {
        return;
    }
    m_busyFlags.controlBusy = true;
    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.controlBusy = false;
        return;
    }
    QVector<quint16> _data;
    _data << data;
    modbus->asyncWrite(addr, _data)
        .fail([this](const QString& error) {
            qDebug() << "Failed to write register for device:" << m_deviceName << "Error:" << error;
        })
        .finally([this]() { m_busyFlags.controlBusy = false; });
}

void DeviceSpikeHelper::control(quint16 optCode)
{
    quint16 addr = SPIKE_CMD_ADDR_MAP.value(optCode, 0);
    quint16 data = SPIKE_CMD_VALUE_MAP.value(optCode, 0);
    control(addr, data);
}

void DeviceSpikeHelper::onGetDeviceData()
{
    if (m_busyFlags.dataBusy) {
        return;
    }
    m_busyFlags.dataBusy = true;

    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.dataBusy = false;
        return;
    }
    modbus->asyncRead(REG_SYSTEM_STATE, 1)
        .then([this, modbus](const QVector<quint16>& result) {
            m_deviceData.status = result[0];
            m_isIdle = m_deviceData.status == STATE_IDLE;
            return modbus->asyncRead(REG_SYSTEM_ERROR, 1);
        })
        .then([this, modbus](const QVector<quint16>& result) {
            m_deviceData.faultCode = result[0];
            if (m_deviceData.faultCode != m_lastFaultCode && m_deviceData.faultCode != ERROR_NONE) {
                emit errorOccurred(QString("错误: %1").arg(getErrorName(m_deviceData.faultCode)));
            }
            m_lastFaultCode = m_deviceData.faultCode;
            return modbus->asyncRead(REG_TIME_YEAR_MONTH, 3);  // 读取3个时间寄存器
        })
        .then([this](const QVector<quint16>& result) {
            // 解析时间数据
            quint16 yearMonth = result[0];
            quint16 dayHour = result[1];
            quint16 minSec = result[2];

            int year = (yearMonth >> 8) & 0xFF;  // 高8位：年
            int month = yearMonth & 0xFF;        // 低8位：月
            int day = (dayHour >> 8) & 0xFF;     // 高8位：日
            int hour = dayHour & 0xFF;           // 低8位：时
            int minute = (minSec >> 8) & 0xFF;   // 高8位：分
            int second = minSec & 0xFF;          // 低8位：秒

            // 年份可能需要处理（如果是两位数年份，需要转换为四位数）
            if (year < 100) {
                year += 2000;  // 假设是21世纪
            }

            QDateTime datetime(QDate(year, month, day), QTime(hour, minute, second));
            m_deviceData.datetime = datetime;
            emit deviceDataChanged(m_deviceData);
        })
        .finally([this]() { m_busyFlags.dataBusy = false; });
}

void DeviceSpikeHelper::setDeviceTime(const QDateTime& datetime)
{
    if (m_busyFlags.controlBusy) {
        return;
    }
    m_busyFlags.controlBusy = true;

    ModbusHelper* modbus = ModbusManager::instance()->getModbusHelper(m_deviceName);
    if (!modbus) {
        qDebug() << "Failed to get ModbusHelper for device:" << m_deviceName;
        m_busyFlags.controlBusy = false;
        return;
    }

    // 准备时间数据
    int year = datetime.date().year();
    int month = datetime.date().month();
    int day = datetime.date().day();
    int hour = datetime.time().hour();
    int minute = datetime.time().minute();
    int second = datetime.time().second();

    // 如果年份是四位数，转换为两位数（取后两位）
    if (year >= 2000) {
        year = year - 2000;
    }

    // 组合寄存器数据
    quint16 yearMonth = ((year & 0xFF) << 8) | (month & 0xFF);  // 年(高8位) + 月(低8位)
    quint16 dayHour = ((day & 0xFF) << 8) | (hour & 0xFF);      // 日(高8位) + 时(低8位)
    quint16 minSec = ((minute & 0xFF) << 8) | (second & 0xFF);  // 分(高8位) + 秒(低8位)

    QVector<quint16> data;
    data << yearMonth << dayHour << minSec;

    modbus->asyncWrite(REG_TIME_YEAR_MONTH, data)
        .fail([this](const QString& error) {
            qDebug() << "Failed to write time registers for device:" << m_deviceName
                     << "Error:" << error;
        })
        .finally([this]() { m_busyFlags.controlBusy = false; });
}
