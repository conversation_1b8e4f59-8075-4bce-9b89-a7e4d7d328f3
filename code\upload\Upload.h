#ifndef UPLOAD_H
#define UPLOAD_H

#include <QMap>
#include <QObject>

#include "UploadConfig.h"
#include "UploadHelper.h"

namespace Upload {

class Upload : public QObject
{
    Q_OBJECT
public:
    static Upload *instance();

    // 配置管理
    QMap<QString, Config> getConfig() const;
    bool setConfig(const QMap<QString, Config> &configMap);

    // 上传控制
    void start();
    void stop();

    // 状态查询
    bool isRunning() const;
    bool isRunning(const QString &key) const;

signals:
    void uploadStarted(const QString &key);
    void uploadStopped(const QString &key);
    void configChanged();

private:
    Upload(QObject *parent = nullptr);
    ~Upload();
    Upload(const Upload &) = delete;
    Upload &operator=(const Upload &) = delete;

    void createUploadHelpers();
    void updateUploadHelpers();

    Configurer *m_configurer;
    QMap<QString, UploadHelper *> m_uploadHelpers;
    bool m_running;
};

}  // namespace Upload

#endif  // UPLOAD_H